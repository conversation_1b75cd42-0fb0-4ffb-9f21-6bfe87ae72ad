# RAT Module Dependencies
# Core networking and encryption
cryptography>=41.0.0
requests>=2.31.0
websockets>=11.0.0
aiohttp>=3.8.0

# System monitoring and control
psutil>=5.9.0
pynput>=1.7.6
pyautogui>=0.9.54

# Image and video processing
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# Audio processing
pyaudio>=0.2.11
sounddevice>=0.4.6

# Windows-specific (optional)
pywin32>=306; sys_platform == "win32"
wmi>=1.5.1; sys_platform == "win32"

# Cross-platform GUI
tkinter-page>=0.1.0
customtkinter>=5.2.0

# Database and storage
sqlite3
sqlalchemy>=2.0.0

# Web interface
flask>=2.3.0
flask-socketio>=5.3.0
jinja2>=3.1.0

# Utilities
colorama>=0.4.6
tqdm>=4.65.0
python-dateutil>=2.8.2
schedule>=1.2.0

# Testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Development tools
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Optional advanced features
scapy>=2.5.0
nmap>=0.0.1
paramiko>=3.3.0
ftplib
smtplib

# Obfuscation and packing
pyinstaller>=5.13.0
cx-freeze>=6.15.0

# Additional security libraries
bcrypt>=4.0.1
jwt>=1.3.1
