#!/bin/bash
# Script to fix all dependency issues in botnet project

echo "🔧 إصلاح مشاكل التبعيات في مشروع Botnet"
echo "=" * 60

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت"
    exit 1
fi

echo "✅ Python3 متوفر: $(python3 --version)"

# إنشاء بيئة افتراضية إذا لم تكن موجودة
if [ ! -d "botnet_env" ]; then
    echo "📦 إنشاء بيئة افتراضية جديدة..."
    python3 -m venv botnet_env
fi

# تفعيل البيئة الافتراضية
echo "🔄 تفعيل البيئة الافتراضية..."
source botnet_env/bin/activate

# تحديث pip
echo "⬆️ تحديث pip..."
pip install --upgrade pip

# تثبيت التبعيات الأساسية المفقودة
echo "📥 تثبيت التبعيات المفقودة..."

# التبعيات الأساسية
pip install psutil>=5.9.0
pip install paramiko>=3.3.0
pip install beautifulsoup4>=4.12.0
pip install pynput>=1.7.6

# التبعيات الإضافية للوحدات المتقدمة
pip install lxml>=4.9.0
pip install selenium>=4.15.0
pip install pyautogui>=0.9.54
pip install opencv-python>=4.8.0
pip install flask-socketio>=5.3.0
pip install sqlalchemy>=2.0.0
pip install colorama>=0.4.6
pip install tqdm>=4.65.0

# تبعيات خاصة بـ Windows (إذا كان النظام Windows)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    pip install pywin32>=306
    pip install wmi>=1.5.1
fi

# التحقق من التثبيت
echo "🔍 التحقق من التبعيات المثبتة..."
python3 -c "
import sys
deps_to_check = ['psutil', 'paramiko', 'beautifulsoup4', 'pynput', 'requests', 'numpy', 'cryptography', 'websockets', 'flask', 'PIL']
failed = []

for dep in deps_to_check:
    try:
        __import__(dep)
        print(f'✅ {dep}')
    except ImportError as e:
        print(f'❌ {dep}: {e}')
        failed.append(dep)

if failed:
    print(f'\n⚠️ فشل في تثبيت: {failed}')
    sys.exit(1)
else:
    print('\n🎉 جميع التبعيات مثبتة بنجاح!')
"

# إنشاء ملف requirements.txt محدث
echo "📝 إنشاء ملف requirements.txt محدث..."
pip freeze > requirements_fixed.txt

echo "✅ تم إصلاح جميع مشاكل التبعيات بنجاح!"
echo "📁 ملف التبعيات الجديد: requirements_fixed.txt"
echo "🔄 لتفعيل البيئة الافتراضية: source botnet_env/bin/activate"
