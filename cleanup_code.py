#!/usr/bin/env python3
"""
أداة تنظيف الكود من المتغيرات والاستيرادات غير المستخدمة
"""

import os
import re
import ast
import sys
from pathlib import Path

class CodeCleaner:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.issues_found = []
        self.files_to_clean = []
        
    def find_unused_imports(self, file_path):
        """البحث عن الاستيرادات غير المستخدمة"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل الكود
            tree = ast.parse(content)
            
            # جمع الاستيرادات
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        imports.append(alias.name)
            
            # البحث عن الاستيرادات غير المستخدمة
            unused_imports = []
            for imp in imports:
                if imp not in content.replace(f"import {imp}", ""):
                    # فحص أكثر دقة
                    pattern = rf'\b{re.escape(imp)}\b'
                    if len(re.findall(pattern, content)) <= 1:  # فقط في سطر الاستيراد
                        unused_imports.append(imp)
            
            return unused_imports
            
        except Exception as e:
            print(f"خطأ في تحليل {file_path}: {e}")
            return []
    
    def find_unused_variables(self, file_path):
        """البحث عن المتغيرات غير المستخدمة"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            unused_vars = []
            
            # البحث عن المتغيرات المعرفة ولكن غير المستخدمة
            for i, line in enumerate(lines, 1):
                # البحث عن تعريف متغيرات
                var_match = re.search(r'^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*=', line.strip())
                if var_match:
                    var_name = var_match.group(1)
                    
                    # تجاهل المتغيرات الخاصة
                    if var_name.startswith('_') or var_name in ['self', 'cls']:
                        continue
                    
                    # البحث عن استخدام المتغير في باقي الملف
                    content = ''.join(lines)
                    pattern = rf'\b{re.escape(var_name)}\b'
                    matches = re.findall(pattern, content)
                    
                    # إذا كان المتغير يظهر مرة واحدة فقط (في التعريف)
                    if len(matches) == 1:
                        unused_vars.append((i, var_name, line.strip()))
            
            return unused_vars
            
        except Exception as e:
            print(f"خطأ في تحليل المتغيرات في {file_path}: {e}")
            return []
    
    def scan_project(self):
        """فحص المشروع بالكامل"""
        print("🔍 فحص المشروع للبحث عن مشاكل الكود...")
        
        # البحث عن جميع ملفات Python
        python_files = list(self.project_path.rglob("*.py"))
        
        for file_path in python_files:
            # تجاهل ملفات معينة
            if any(skip in str(file_path) for skip in ['__pycache__', '.git', 'venv', 'env']):
                continue
            
            print(f"📄 فحص: {file_path.relative_to(self.project_path)}")
            
            # البحث عن الاستيرادات غير المستخدمة
            unused_imports = self.find_unused_imports(file_path)
            if unused_imports:
                self.issues_found.append({
                    'file': file_path,
                    'type': 'unused_imports',
                    'items': unused_imports
                })
            
            # البحث عن المتغيرات غير المستخدمة
            unused_vars = self.find_unused_variables(file_path)
            if unused_vars:
                self.issues_found.append({
                    'file': file_path,
                    'type': 'unused_variables',
                    'items': unused_vars
                })
    
    def generate_report(self):
        """إنشاء تقرير بالمشاكل المكتشفة"""
        print("\n📊 تقرير مشاكل الكود:")
        print("=" * 60)
        
        if not self.issues_found:
            print("✅ لم يتم العثور على مشاكل!")
            return
        
        for issue in self.issues_found:
            file_path = issue['file'].relative_to(self.project_path)
            print(f"\n📁 الملف: {file_path}")
            
            if issue['type'] == 'unused_imports':
                print("  🔸 استيرادات غير مستخدمة:")
                for imp in issue['items']:
                    print(f"    - {imp}")
            
            elif issue['type'] == 'unused_variables':
                print("  🔸 متغيرات غير مستخدمة:")
                for line_num, var_name, line_content in issue['items']:
                    print(f"    - السطر {line_num}: {var_name}")
                    print(f"      {line_content}")
    
    def fix_known_issues(self):
        """إصلاح المشاكل المعروفة من التحليل السابق"""
        print("\n🔧 إصلاح المشاكل المعروفة...")
        
        # إصلاح bot_real.py
        bot_real_path = self.project_path / "bot_real.py"
        if bot_real_path.exists():
            self.fix_bot_real_file(bot_real_path)
        
        # إصلاح ملفات أخرى حسب الحاجة
        print("✅ تم إصلاح المشاكل المعروفة")
    
    def fix_bot_real_file(self, file_path):
        """إصلاح مشاكل محددة في bot_real.py"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إزالة استيراد shutil غير المستخدم
            if 'import shutil' in content and 'shutil.' not in content:
                content = re.sub(r'^import shutil\n', '', content, flags=re.MULTILINE)
                print(f"  ✅ إزالة استيراد shutil غير المستخدم من {file_path.name}")
            
            # إصلاح دالة is_safe_command
            old_function = r'def is_safe_command\(self, command\):\s*"""[^"]*"""\s*return True'
            new_function = '''def is_safe_command(self, command):
        """No command filtering - all commands allowed"""
        # Command parameter is intentionally unused in unrestricted mode
        return True  # Allow all commands without restrictions'''
            
            if re.search(old_function, content, re.DOTALL):
                content = re.sub(old_function, new_function, content, flags=re.DOTALL)
                print(f"  ✅ إصلاح دالة is_safe_command في {file_path.name}")
            
            # حفظ الملف المحدث
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")

def main():
    project_path = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    cleaner = CodeCleaner(project_path)
    
    # فحص المشروع
    cleaner.scan_project()
    
    # إنشاء تقرير
    cleaner.generate_report()
    
    # إصلاح المشاكل المعروفة
    cleaner.fix_known_issues()
    
    print(f"\n📈 إجمالي المشاكل المكتشفة: {len(cleaner.issues_found)}")

if __name__ == "__main__":
    main()
