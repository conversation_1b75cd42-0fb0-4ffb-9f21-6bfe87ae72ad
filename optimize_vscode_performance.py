#!/usr/bin/env python3
"""
VS Code Performance Optimizer for Botnet Lab Project
أداة تحسين أداء VS Code لمشروع Botnet Lab
"""

import os
import sys
import json
import shutil
import subprocess
import time
from pathlib import Path

class VSCodeOptimizer:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.vscode_dir = self.project_path / ".vscode"
        self.performance_issues = []
        self.optimizations_applied = []
        
    def analyze_project_size(self):
        """تحليل حجم المشروع وتحديد المشاكل المحتملة"""
        print("🔍 تحليل حجم وتعقيد المشروع...")
        
        # حساب عدد الملفات
        total_files = sum(1 for _ in self.project_path.rglob("*") if _.is_file())
        python_files = sum(1 for _ in self.project_path.rglob("*.py"))
        db_files = list(self.project_path.rglob("*.db")) + list(self.project_path.rglob("*.sqlite*"))
        
        # حساب حجم المجلدات الكبيرة
        large_dirs = []
        for item in self.project_path.iterdir():
            if item.is_dir():
                try:
                    size = sum(f.stat().st_size for f in item.rglob("*") if f.is_file())
                    if size > 50 * 1024 * 1024:  # أكبر من 50MB
                        large_dirs.append((item.name, size / (1024 * 1024)))
                except:
                    pass
        
        print(f"📊 إحصائيات المشروع:")
        print(f"   إجمالي الملفات: {total_files:,}")
        print(f"   ملفات Python: {python_files:,}")
        print(f"   قواعد البيانات: {len(db_files)}")
        print(f"   المجلدات الكبيرة: {len(large_dirs)}")
        
        # تحديد المشاكل المحتملة
        if total_files > 10000:
            self.performance_issues.append(f"عدد كبير من الملفات ({total_files:,})")
        
        if python_files > 1000:
            self.performance_issues.append(f"عدد كبير من ملفات Python ({python_files:,})")
        
        if len(db_files) > 5:
            self.performance_issues.append(f"عدد كبير من قواعد البيانات ({len(db_files)})")
        
        for dir_name, size in large_dirs:
            self.performance_issues.append(f"مجلد كبير: {dir_name} ({size:.1f} MB)")
        
        return {
            'total_files': total_files,
            'python_files': python_files,
            'db_files': len(db_files),
            'large_dirs': large_dirs
        }
    
    def clean_cache_files(self):
        """تنظيف ملفات الكاش والملفات المؤقتة"""
        print("🧹 تنظيف ملفات الكاش...")
        
        cache_patterns = [
            "**/__pycache__",
            "**/*.pyc",
            "**/*.pyo",
            "**/.pytest_cache",
            "**/temp",
            "**/tmp",
            "**/.DS_Store",
            "**/Thumbs.db"
        ]
        
        cleaned_count = 0
        for pattern in cache_patterns:
            for item in self.project_path.rglob(pattern.replace("**/", "")):
                try:
                    if item.is_file():
                        item.unlink()
                        cleaned_count += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        cleaned_count += 1
                except:
                    pass
        
        if cleaned_count > 0:
            self.optimizations_applied.append(f"تم تنظيف {cleaned_count} ملف/مجلد كاش")
            print(f"✅ تم تنظيف {cleaned_count} ملف/مجلد")
        else:
            print("✅ لا توجد ملفات كاش للتنظيف")
    
    def create_gitignore(self):
        """إنشاء ملف .gitignore محسن"""
        print("📝 إنشاء ملف .gitignore محسن...")
        
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
botnet_env/
venv/
env/
ENV/

# Database files
*.db
*.sqlite*
*.db-journal

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# IDE
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/

# Data files
data/
*.csv
*.json.bak
"""
        
        gitignore_path = self.project_path / ".gitignore"
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        self.optimizations_applied.append("تم إنشاء ملف .gitignore محسن")
        print("✅ تم إنشاء ملف .gitignore")
    
    def optimize_vscode_settings(self):
        """تحسين إعدادات VS Code"""
        print("⚙️ تحسين إعدادات VS Code...")
        
        # التأكد من وجود مجلد .vscode
        self.vscode_dir.mkdir(exist_ok=True)
        
        # إعدادات محسنة للأداء
        settings = {
            "search.exclude": {
                "**/botnet_env/**": True,
                "**/__pycache__/**": True,
                "**/*.pyc": True,
                "**/*.db": True,
                "**/*.sqlite*": True,
                "**/logs/**": True,
                "**/data/**": True,
                "**/temp/**": True,
                "**/tmp/**": True
            },
            "files.watcherExclude": {
                "**/botnet_env/**": True,
                "**/__pycache__/**": True,
                "**/logs/**": True,
                "**/data/**": True,
                "**/*.db": True,
                "**/*.sqlite*": True
            },
            "python.analysis.autoImportCompletions": False,
            "python.analysis.indexing": False,
            "python.analysis.memory.keepLibraryAst": False,
            "editor.minimap.enabled": False,
            "editor.codeLens": False,
            "git.autorefresh": False,
            "extensions.autoUpdate": False,
            "telemetry.telemetryLevel": "off"
        }
        
        settings_path = self.vscode_dir / "settings.json"
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=4, ensure_ascii=False)
        
        self.optimizations_applied.append("تم تحسين إعدادات VS Code")
        print("✅ تم تحسين إعدادات VS Code")
    
    def check_system_resources(self):
        """فحص موارد النظام"""
        print("💻 فحص موارد النظام...")
        
        try:
            # فحص الذاكرة
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
            
            mem_total = None
            mem_available = None
            for line in meminfo.split('\n'):
                if 'MemTotal:' in line:
                    mem_total = int(line.split()[1]) // 1024  # MB
                elif 'MemAvailable:' in line:
                    mem_available = int(line.split()[1]) // 1024  # MB
            
            if mem_total and mem_available:
                mem_usage = ((mem_total - mem_available) / mem_total) * 100
                print(f"📊 الذاكرة: {mem_available} MB متاحة من {mem_total} MB ({mem_usage:.1f}% مستخدمة)")
                
                if mem_available < 1024:  # أقل من 1GB
                    self.performance_issues.append("ذاكرة منخفضة متاحة")
            
            # فحص المعالج
            try:
                load_avg = os.getloadavg()
                print(f"📊 حمولة المعالج: {load_avg[0]:.2f}")
                
                if load_avg[0] > 2.0:
                    self.performance_issues.append("حمولة عالية على المعالج")
            except:
                pass
                
        except Exception as e:
            print(f"⚠️ لا يمكن قراءة معلومات النظام: {e}")
    
    def create_performance_script(self):
        """إنشاء سكريبت لمراقبة الأداء"""
        print("📜 إنشاء سكريبت مراقبة الأداء...")
        
        script_content = '''#!/bin/bash
# VS Code Performance Monitor
# مراقب أداء VS Code

echo "🔍 مراقبة أداء VS Code..."

# فحص عمليات VS Code
echo "📊 عمليات VS Code النشطة:"
ps aux | grep -i "code" | grep -v grep | head -5

echo ""
echo "💾 استهلاك الذاكرة:"
ps aux | grep -i "code" | grep -v grep | awk '{sum+=$6} END {print "إجمالي استهلاك الذاكرة: " sum/1024 " MB"}'

echo ""
echo "🔄 استهلاك المعالج:"
top -b -n1 | grep -i "code" | head -3

echo ""
echo "📁 حجم مجلد المشروع:"
du -sh .

echo ""
echo "🗂️ عدد الملفات المفتوحة:"
lsof | grep -i "code" | wc -l
'''
        
        script_path = self.project_path / "tools" / "monitor_vscode_performance.sh"
        script_path.parent.mkdir(exist_ok=True)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # جعل السكريبت قابل للتنفيذ
        os.chmod(script_path, 0o755)
        
        self.optimizations_applied.append("تم إنشاء سكريبت مراقبة الأداء")
        print("✅ تم إنشاء سكريبت مراقبة الأداء")
    
    def generate_report(self):
        """إنشاء تقرير التحسين"""
        print("\n📋 تقرير تحسين أداء VS Code")
        print("=" * 50)
        
        if self.performance_issues:
            print("\n⚠️ المشاكل المكتشفة:")
            for issue in self.performance_issues:
                print(f"   • {issue}")
        else:
            print("\n✅ لم يتم اكتشاف مشاكل أداء كبيرة")
        
        if self.optimizations_applied:
            print("\n🔧 التحسينات المطبقة:")
            for optimization in self.optimizations_applied:
                print(f"   • {optimization}")
        
        print("\n💡 توصيات إضافية:")
        print("   • أعد تشغيل VS Code بعد التحسينات")
        print("   • فكر في استخدام VS Code Insiders للأداء الأفضل")
        print("   • قم بإغلاق الإضافات غير الضرورية")
        print("   • استخدم مجلدات منفصلة للمشاريع الكبيرة")
        
        # حفظ التقرير في ملف
        report_path = self.project_path / "tools" / "vscode_optimization_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("تقرير تحسين أداء VS Code\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"تاريخ التحسين: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if self.performance_issues:
                f.write("المشاكل المكتشفة:\n")
                for issue in self.performance_issues:
                    f.write(f"- {issue}\n")
                f.write("\n")
            
            if self.optimizations_applied:
                f.write("التحسينات المطبقة:\n")
                for optimization in self.optimizations_applied:
                    f.write(f"- {optimization}\n")
        
        print(f"\n📄 تم حفظ التقرير في: {report_path}")
    
    def run_optimization(self):
        """تشغيل عملية التحسين الكاملة"""
        print("🚀 بدء تحسين أداء VS Code لمشروع Botnet Lab")
        print("=" * 60)
        
        # تحليل المشروع
        self.analyze_project_size()
        
        # فحص موارد النظام
        self.check_system_resources()
        
        # تطبيق التحسينات
        self.clean_cache_files()
        self.create_gitignore()
        self.optimize_vscode_settings()
        self.create_performance_script()
        
        # إنشاء التقرير
        self.generate_report()
        
        print("\n🎉 تم الانتهاء من تحسين الأداء!")
        print("🔄 يُنصح بإعادة تشغيل VS Code الآن")

def main():
    """الدالة الرئيسية"""
    project_path = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    if not os.path.exists(project_path):
        print(f"❌ مسار المشروع غير موجود: {project_path}")
        return
    
    optimizer = VSCodeOptimizer(project_path)
    optimizer.run_optimization()

if __name__ == "__main__":
    main()
